/target:library
/out:Temp/Unity.PlasticSCM.Editor.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Unity.PlasticSCM.Editor.dll.ref
/deterministic
/debug:portable
/optimize+
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/unityplastic.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_INPUT_SYSTEM
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_42
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:USE_SEARCH_ENGINE_API
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\ApplicationDataPath.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssemblyInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetMenu\AssetFilesFilterPatternsMenuBuilder.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetMenu\AssetMenuItems.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetMenu\AssetMenuOperations.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetMenu\AssetOperations.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetMenu\AssetsSelection.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetMenu\Dialogs\CheckinDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetMenu\Dialogs\CheckinDialogOperations.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetMenu\ProjectViewAssetSelection.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetOverlays\AssetStatus.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetOverlays\Cache\AssetStatusCache.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetOverlays\Cache\BuildPathDictionary.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetOverlays\Cache\LocalStatusCache.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetOverlays\Cache\LockStatusCache.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetOverlays\Cache\RemoteStatusCache.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetOverlays\Cache\SearchLocks.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetOverlays\DrawAssetOverlay.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\AssetsPath.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\GetSelectedPaths.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\LoadAsset.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\Processor\AssetModificationProcessor.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\Processor\AssetPostprocessor.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\Processor\AssetsProcessor.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\Processor\PlasticAssetsProcessor.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\Processor\WorkspaceOperationsMonitor.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\ProjectPath.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\RefreshAsset.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\RepaintInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AssetsUtils\SaveAssets.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\AutoRefresh.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\CheckWorkspaceTreeNodeStatus.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\CollabMigration\CloudProjectId.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\CollabMigration\MigrateCollabProject.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\CollabMigration\MigrationDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\CollabMigration\MigrationProgressRender.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\CollabPlugin.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\AutoConfig.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\ChannelCertificateUiImpl.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\AutoLogin.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\CloudEditionWelcomeWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\OrganizationPanel.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\SignInPanel.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\SignInWithEmailPanel.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\WaitingSignInPanel.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\ConfigurePartialWorkspace.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\CredentialsDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\CredentialsUIImpl.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\EncryptionConfigurationDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\MissingEncryptionPasswordPromptHandler.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\SSOCredentialsDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\TeamEdition\TeamEditionConfigurationWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\ToolConfig.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Configuration\WriteLogConfiguration.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Developer\CheckinProgress.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Developer\GenericProgress.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Developer\IncomingChangesNotifier.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Developer\ProgressOperationHandler.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Developer\UpdateProgress.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportLineListViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportListHeaderState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportListView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\DrawGuiModeSwitcher.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\EnumExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\FindWorkspace.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\GetRelativePath.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Gluon\CheckinProgress.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Gluon\IncomingChangesNotifier.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Gluon\ProgressOperationHandler.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Gluon\UpdateProgress.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Gluon\UpdateReport\ErrorListViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportListHeaderState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportListView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Help\BuildFormattedHelp.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Help\DrawHelpPanel.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Help\ExternalLink.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Help\HelpData.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Help\HelpFormat.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Help\HelpLink.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Help\HelpLinkData.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Help\HelpPanel.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Help\TestingHelpData.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Hub\CommandLineArguments.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Hub\Operations\CreateWorkspace.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Hub\Operations\DownloadRepository.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Hub\Operations\OperationParams.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Hub\ParseArguments.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Hub\ProcessCommand.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Inspector\DrawInspectorOperations.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Inspector\InspectorAssetSelection.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\MetaPath.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\NewIncomingChanges.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\ParentWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\PlasticApp.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\PlasticConnectionMonitor.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\PlasticMenuItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\PlasticNotification.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\PlasticPlugin.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\PlasticPluginIsEnabledPreference.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\PlasticProjectSettingsProvider.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\PlasticWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\ProjectWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\QueryVisualElementsExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\SceneView\DrawSceneOperations.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\SwitchModeConfirmationDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Tool\AuthToken.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Tool\BringWindowToFront.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Tool\FindTool.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Tool\IsExeAvailable.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Tool\LaunchInstaller.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Tool\LaunchTool.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Tool\ToolConstants.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Avatar\ApplyCircleMask.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Avatar\AvatarImages.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Avatar\GetAvatar.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\BoolSetting.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\CloseWindowIfOpened.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\CooldownWindowDelayer.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\DockEditorWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\DrawActionButton.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\DrawActionHelpBox.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\DrawActionToolbar.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\DrawSearchField.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\DrawSplitter.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\DrawTextBlockWithEndLink.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\DrawUserIcon.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\DropDownTextField.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\EditorDispatcher.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\EditorProgressBar.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\EditorProgressControls.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\EditorVersion.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\EditorWindowFocus.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\EnumPopupSetting.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\FindEditorWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\GUIActionRunner.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\GUISpace.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\GetPlasticShortcut.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\GuiEnabled.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\HandleMenuItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Images.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\MeasureMaxWidth.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Message\DrawDialogIcon.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Message\PlasticQuestionAlert.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\OverlayRect.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\PlasticDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\PlasticSplitterGUILayout.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Progress\DrawProgressForDialogs.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Progress\DrawProgressForMigration.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Progress\DrawProgressForOperations.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Progress\DrawProgressForViews.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Progress\OperationProgressData.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Progress\ProgressControlsForDialogs.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Progress\ProgressControlsForMigration.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Progress\ProgressControlsForViews.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\ResponseType.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\RunModal.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\ScreenResolution.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\ShowWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\SortOrderComparer.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\StatusBar\IncomingChangesNotification.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\StatusBar\NotificationBar.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\StatusBar\StatusBar.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\TabButton.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Tree\DrawTreeViewEmptyState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Tree\DrawTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Tree\GetChangesOverlayIcon.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Tree\ListViewItemIds.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Tree\TableViewOperations.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Tree\TreeHeaderColumns.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Tree\TreeHeaderSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\Tree\TreeViewItemIds.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\UIElements\LoadingSpinner.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\UIElements\ProgressControlsForDialogs.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\UIElements\UIElementsExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\UnityConstants.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\UnityEvents.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\UnityMenuItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\UnityPlasticGuiMessage.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\UnityPlasticTimer.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\UnityStyles.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UI\UnityThreadWaiter.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\UnityConfigurationChecker.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\VCSPlugin.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\ViewSwitcher.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Branch\BranchListViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Branch\BranchesListHeaderState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Branch\BranchesListView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Branch\BranchesSelection.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Branch\BranchesTab.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Branch\BranchesViewMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Branch\CreateBranchDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Branch\Dialogs\RenameBranchDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Changesets\ChangesetListViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Changesets\ChangesetsListHeaderState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Changesets\ChangesetsListView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Changesets\ChangesetsSelection.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Changesets\ChangesetsTab.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Changesets\ChangesetsTab_Operations.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Changesets\ChangesetsViewMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Changesets\DateFilter.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Changesets\LaunchDiffOperations.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\ConfirmContinueWithPendingChangesDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\CreateWorkspace\CreateWorkspaceView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\CreateWorkspace\CreateWorkspaceViewState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\CreateRepositoryDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoriesListHeaderState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoriesListView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoryExplorerDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoryListViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\CreateWorkspace\DrawCreateWorkspaceView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\CreateWorkspace\PerformInitialCheckin.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\CreateWorkspace\ValidRepositoryName.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Diff\ChangeCategoryTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Diff\ClientDiffTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Diff\Dialogs\GetRestorePathDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Diff\DiffPanel.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Diff\DiffSelection.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Diff\DiffTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Diff\DiffTreeViewMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Diff\GetClientDiffInfos.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Diff\MergeCategoryTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Diff\UnityDiffTree.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\DownloadPlasticExeWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\FileSystemOperation.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\History\HistoryListHeaderState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\History\HistoryListView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\History\HistoryListViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\History\HistoryListViewMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\History\HistorySelection.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\History\HistoryTab.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\History\SaveAction.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\ChangeCategoryTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\ChangeTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\DirectoryConflicts\ConflictResolutionState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\DirectoryConflicts\DrawDirectoryResolutionPanel.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesSelection.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTab.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTreeHeaderState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesViewMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\IsCurrent.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\IsResolved.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Developer\UnityIncomingChangesTree.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\DrawIncomingChangesOverview.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Gluon\ChangeCategoryTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Gluon\ChangeTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorListViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorsListHeaderState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorsListView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesSelection.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTab.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTreeHeaderState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesViewMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\Gluon\UnityIncomingChangesTree.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\IncomingChanges\IIncomingChangesTab.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\ChangeCategoryTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\ChangeTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\ChangelistTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\Changelists\ChangelistMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\Changelists\MoveToChangelistMenuBuilder.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\Dialogs\CheckinConflictsDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\Dialogs\CreateChangelistDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\Dialogs\DependenciesDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\Dialogs\EmptyCheckinMessageDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\Dialogs\FilterRulesConfirmationDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\Dialogs\LaunchCheckinConflictsDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\Dialogs\LaunchDependenciesDialog.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\DrawCommentTextArea.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\FilesFilterPatternsMenuBuilder.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\PendingChangesMultiColumnHeader.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\PendingChangesSelection.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTab.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTab_Operations.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTreeHeaderState.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\PendingChangesViewMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\PendingChangesViewPendingChangeMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\PendingMergeLinks\MergeLinkListViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\PendingMergeLinks\MergeLinksListView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\PendingChanges\UnityPendingChangesTree.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Welcome\DownloadAndInstallOperation.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Welcome\GetInstallerTmpFileName.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Welcome\MacOSConfigWorkaround.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\Views\Welcome\WelcomeView.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\VisualElementExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\WebApi\ChangesetFromCollabCommitResponse.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\WebApi\CredentialsResponse.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\WebApi\CurrentUserAdminCheckResponse.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\WebApi\IsCollabProjectMigratedResponse.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\WebApi\OrganizationCredentials.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\WebApi\TokenExchangeResponse.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\WebApi\WebRestApiClient.cs
H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0\Editor\PlasticSCM\WorkspaceWindow.cs
