/target:library
/out:Temp/Unity.Timeline.Editor.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Unity.Timeline.Editor.dll.ref
/deterministic
/debug:portable
/optimize+
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/Unity.Timeline.dll
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:Assets/AstarPathfindingProject/Plugins/DotNetZip/Pathfinding.Ionic.Zip.Reduced.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/AsyncIO.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/NaCl.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/NetMQ.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Release/netstandard2.0/UnityCodeAssistSynchronizerModel.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Serilog.Sinks.PersistentFile.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Serilog.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Buffers.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Memory.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Runtime.CompilerServices.Unsafe.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Threading.Tasks.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/log4netPlastic.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/unityplastic.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.0.2/Runtime/Newtonsoft.Json.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.render-pipelines.core@10.10.1/Editor/ShaderGenerator/ICSharpCode.NRefactory.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_INPUT_SYSTEM
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_42
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:USE_SEARCH_ENGINE_API
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\ActionContext.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\ActionManager.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\ClipAction.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\ClipsActions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\IAction.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\IMenuChecked.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\IMenuName.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\Invoker.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\MarkerAction.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\MarkerActions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\Menus\MenuItemActionBase.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\Menus\TimelineContextMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\TimelineAction.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\TimelineActions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\TrackAction.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\TrackActions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Activation\ActivationTrackEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Activation\ActivationTrackInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Analytics\TimelineAnalytics.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationClipActions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationClipCurveCache.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationClipExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationOffsetMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationPlayableAssetEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationTrackActions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\BindingSelector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\BindingTreeViewDataSource.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\BindingTreeViewDataSourceGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\ClipCurveEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\CurveDataSource.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\CurveTreeViewNode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\CurvesProxy.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\TimelineAnimationUtilities.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Attributes\ActiveInModeAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Attributes\MenuEntryAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Attributes\ShortcutAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Attributes\TimelineShortcutAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Audio\AudioClipPropertiesDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Audio\AudioPlayableAssetEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Audio\AudioPlayableAssetInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Audio\AudioTrackInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\ControlTrack\ControlPlayableAssetEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CurveEditUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CustomEditors\ClipEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CustomEditors\CustomTimelineEditorCache.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CustomEditors\MarkerEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CustomEditors\MarkerTrackEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CustomEditors\TrackEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\DirectorNamedColor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\DirectorStyles.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Extensions\AnimatedParameterExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Extensions\AnimationTrackExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Extensions\TrackExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\ClipItem.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\ITimelineItem.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\ItemsGroup.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\ItemsPerTrack.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\ItemsUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\MarkerItem.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\AddDelete\AddDeleteItemModeMix.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\AddDelete\AddDeleteItemModeReplace.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\AddDelete\AddDeleteItemModeRipple.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\AddDelete\IAddDeleteItemMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Cursors\TimelineCursors.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\EditMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\EditModeInputHandler.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\IMoveItemMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\MoveItemHandler.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\MoveItemModeMix.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\MoveItemModeReplace.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\MoveItemModeRipple.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\MovingItems.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\EaseClip.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\Jog.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\MarkerHeaderContextMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\RectangleSelect.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\RectangleTool.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\RectangleZoom.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\SelectAndMoveItem.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\TrackZoom.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\TrimClip.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\TimeAreaAutoPanner.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\TimeIndicator.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\TimelineClipGroup.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Trim\ITrimItemMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Trim\TrimItemModeMix.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Trim\TrimItemModeReplace.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Trim\TrimItemModeRipple.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\EditModeGUIUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\EditModeMixUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\EditModeReplaceUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\EditModeRippleUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\EditModeUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\ManipulatorsUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\PlacementValidity.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\MenuPriority.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Playables\ControlPlayableInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Properties\AssemblyInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\AnimationTrackRecorder.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\TimelineRecording.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\TimelineRecordingContextualResponder.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\TimelineRecording_Monobehaviour.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\TimelineRecording_PlayableAsset.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\TrackAssetRecordingExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Shortcuts.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalAssetInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalEmitterEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalEmitterInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalEventDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalManager.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalReceiverHeader.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalReceiverInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\Styles.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\TreeView\SignalListFactory.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\TreeView\SignalReceiverItem.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\TreeView\SignalReceiverTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\State\ISequenceState.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\State\SequenceHierarchy.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\State\SequencePath.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\State\SequenceState.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\State\WindowState.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\TimelineEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\TimelineHelpers.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\TimelineSelection.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\TimelineUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Tooltip.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Trackhead.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Undo\ApplyDefaultUndoAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Undo\UndoExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Undo\UndoScope.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\UnityEditorInternals.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\AnimatedParameterCache.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\AnimatedParameterUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\AnimatedPropertyUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\BindingUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\BreadcrumbDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\ClipModifier.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Clipboard.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\ControlPlayableUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\CustomTrackDrawerAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\DisplayNameHelper.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Graphics.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\KeyTraverser.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\MarkerModifier.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\ObjectExtension.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\ObjectReferenceField.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\PropertyCollector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Range.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\GUIColorOverride.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\GUIGroupScope.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\GUIMixedValueScope.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\GUIViewportScope.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\HorizontalScope.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\IndentLevelScope.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\LabelWidthScope.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\PropertyScope.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\SequenceSelectorNameFormater.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\SpacePartitioner.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\StyleManager.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\StyleNormalColorOverride.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\TimeReferenceUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\TimelineKeyboardNavigation.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\TrackModifier.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\TrackResourceCache.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\TypeUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimeReferenceMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineActiveMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineAssetEditionMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineDisabledMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineInactiveMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineReadOnlyMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\PlaybackScroller.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineMarkerHeaderGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindowTimeControl.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_ActiveTimeline.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_Breadcrumbs.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_Duration.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_EditorCallbacks.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_Gui.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_HeaderGui.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_Manipulators.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_PlayRange.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_PlayableLookup.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_PreviewPlayMode.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_Selection.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_StateChange.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_TimeArea.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_TimeCursor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_TrackGui.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\ViewModel\ScriptableObjectViewPrefs.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\ViewModel\TimelineAssetViewModel.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\ViewModel\TimelineWindowViewPrefs.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\WindowConstants.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\AnimationPlayableAssetInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\AnimationTrackInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\BasicAssetInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\BuiltInCurvePresets.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\ClipInspector\ClipInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\ClipInspector\ClipInspectorCurveEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\ClipInspector\ClipInspectorSelectionInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\CurvesOwner\CurvesOwnerInspectorHelper.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\CurvesOwner\ICurvesOwnerInspectorWrapper.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\DirectorNamedColorInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\EditorClip.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\EditorClipFactory.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\GroupTrackInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\MarkerInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TimeFieldDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TimelineAssetInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TimelineInspectorUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TimelinePreferences.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TimelineProjectSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TrackAssetInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\AnimationTrackKeyDataSource.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Control.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\AnimationTrackDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\ClipDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\InfiniteTrackDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\Layers\ClipsLayer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\Layers\ItemsLayer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\Layers\MarkersLayer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\TrackDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\TrackItemsDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\IPropertyKeyDataSource.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\IRowGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ItemGui\ISelectable.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ItemGui\TimelineClipGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ItemGui\TimelineItemGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ItemGui\TimelineMarkerClusterGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ItemGui\TimelineMarkerGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ManipulationsClips.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ManipulationsTimeline.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ManipulationsTracks.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Manipulator.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\PickerUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Snapping\IAttractable.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Snapping\ISnappable.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Snapping\SnapEngine.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineClipHandle.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineClipUnion.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineDataSource.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineDragging.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineTreeViewGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\InlineCurveEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\TimelineGroupGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\TimelineTrackBaseGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\TimelineTrackErrorGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\TimelineTrackGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\TrackResizeHandle.cs
H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackPropertyCurvesDataSource.cs
