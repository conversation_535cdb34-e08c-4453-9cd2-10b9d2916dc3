/target:library
/out:Temp/Unity.Addressables.Editor.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Unity.Addressables.Editor.dll.ref
/deterministic
/debug:portable
/optimize+
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/Unity.Addressables.dll
/reference:Library/ScriptAssemblies/Unity.ResourceManager.dll
/reference:Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.Editor.dll
/reference:Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.dll
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:Assets/AstarPathfindingProject/Plugins/DotNetZip/Pathfinding.Ionic.Zip.Reduced.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/AsyncIO.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/NaCl.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/NetMQ.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Release/netstandard2.0/UnityCodeAssistSynchronizerModel.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Serilog.Sinks.PersistentFile.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Serilog.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Buffers.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Memory.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Runtime.CompilerServices.Unsafe.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Threading.Tasks.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/log4netPlastic.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/unityplastic.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.0.2/Runtime/Newtonsoft.Json.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.render-pipelines.core@10.10.1/Editor/ShaderGenerator/ICSharpCode.NRefactory.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_INPUT_SYSTEM
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:NONRECURSIVE_DEPENDENCY_DATA
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_42
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:USE_SEARCH_ENGINE_API
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\AddressableAssetSettingsDefaultObject.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\AddressableEditorInitialization.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\AssemblyInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AddressableAnalytics.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AddressableAssetSettingsLocator.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AddressablesBuildScriptHooks.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AddressablesDataBuilderInput.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AddressablesDataBuilders.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AddressablesPlayerBuildProcessor.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AnalyzeRules\AnalyzeResultData.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AnalyzeRules\AnalyzeRule.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AnalyzeRules\AnalyzeSystem.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AnalyzeRules\BuildBundleLayout.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AnalyzeRules\BundleRuleBase.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AnalyzeRules\CheckBundleDupeDependencies.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AnalyzeRules\CheckResourcesDupeDependencies.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\AnalyzeRules\CheckSceneDupeDependencies.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\BuildPipelineTasks\AddHashToBundleNameTask.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\BuildPipelineTasks\BuildLayoutGenerationTask.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\BuildPipelineTasks\ExtractDataTask.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\BuildPipelineTasks\GenerateLocationListsTask.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\BuildUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\CcdBuildEvents.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\ContentUpdateScript.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\DataBuilderInterfaces.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\DataBuilders\AddressableAssetsBuildContext.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\DataBuilders\AddressableAssetsBundleBuildParameters.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\DataBuilders\BuildScriptBase.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\DataBuilders\BuildScriptFastMode.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\DataBuilders\BuildScriptPackedMode.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\DataBuilders\BuildScriptPackedPlayMode.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\DataBuilders\BuildScriptVirtualMode.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\DirectoryUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\FastModeInitializationOperation.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\FileRegistry.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\Layout\BuildLayout.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\Layout\BuildLayoutEnums.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\Layout\BuildLayoutHelpers.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\Layout\BuildLayoutPrinter.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\Layout\BuildLayoutSummary.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\LinkXMLGenerator.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\MonoScriptBundleNaming.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\RevertUnchangedAssetsToPreviousAssetState.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\SceneManagerState.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Build\ShaderBundleNaming.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\BuildReportListView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\BuildReportWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\ContentView\AssetsContentView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\ContentView\BundlesContentView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\ContentView\ContentView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\ContentView\DetailsView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\ContentView\DuplicatedAssetsContentView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\ContentView\GroupsContentView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\ContentView\LabelsContentView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\DetailsPanel\DetailsContentView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\DetailsPanel\DetailsContents.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\DetailsPanel\DetailsSummaryView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\DetailsPanel\DetailsUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\IAddressableView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\IBuildReportConsumer.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\MainPanelSummaryTab.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\MainToolbar.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\ToggleTextExpansionButton.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\Utility\BuildReportHelperConsumer.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\Utility\BuildReportUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\Utility\DetailsListItem.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\Utility\DetailsStack.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\Utility\DetailsSummaryBuilder.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\Utility\SummaryRowBuilder.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\BuildReportVisualizer\Utility\TreeBuilder.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\AddressableIconNames.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Data\EventDataPlayerSession.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Data\EventDataPlayerSessionCollection.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Data\EventDataSample.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Data\EventDataSet.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Data\EventDataStream.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\EventGraphListView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\EventListView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\EventViewerWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\Graph\GraphDefinition.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\Graph\GraphLayerBackground.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\Graph\GraphLayerBarChart.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\Graph\GraphLayerBase.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\Graph\GraphLayerEventMarker.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\Graph\GraphLayerLabel.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\Graph\GraphUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\Graph\IGraphLayer.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\GUI\GraphColors.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\AddressablesProfilerDetailsDataInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\AddressablesProfilerDetailsTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\AddressablesProfilerDetailsView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\AddressablesProfilerModule.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\AddressablesProfilerUnsupported.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\AddressablesProfilerViewController.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\BuildLayoutsManager.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\ContentDataListView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\ContentDataTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\ContentSearch.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\GUIElements\AssetLabel.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\HelpDisplayManager.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\ProfilerDetailsTreeViewItemData.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\ProfilerGUIUtilities.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\ProfilerStrings.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\ProfilerTemplates.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\UXML\HelpDisplay.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\UXML\InspectorPane.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\UXML\MissingReport.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\UXML\TreeColumnNames.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\Profiler\UXML\TreeViewPane.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\ResourceManagerCacheWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Diagnostics\ResourceProfilerWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AddressableAssetEntryCollectionEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AddressableAssetGroupInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AddressableAssetGroupTemplateInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AddressableAssetSettingsInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AddressableAssetsSettingsGroupEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AddressableAssetsSettingsGroupEditorBuildMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AddressableAssetsSettingsGroupTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AddressableAssetsSettingsLabelMaskPopup.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AddressableAssetsWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AddressableReadOnlyAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AddressablesGUIUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AnalyzeRuleGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AnalyzeWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AssetInspectorGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AssetLabelReferenceDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AssetPublishEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AssetReferenceDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\AssetSettingsAnalyzeTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\BuildProfileSettingsEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\CacheInitializationDataDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\ContentUpdatePreviewWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\GUIElements\DocumentationButton.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\GUIElements\GUIUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\GUIElements\LabeledLabel.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\GUIElements\Ribbon.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\GUIElements\RibbonButton.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\GUIElements\SearchStringFilters.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\GUIElements\VisualElementsWrapper.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\HostingServicesAddServiceWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\HostingServicesListTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\HostingServicesProfileVarsTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\HostingServicesWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\LabelWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\ProfileDataSourceDropdownWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\ProfileTreeView.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\ProfileValueReferenceDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\ProfileWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\GUI\SerializedTypeDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\HostingServices\BaseHostingService.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\HostingServices\HostingServicesManager.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\HostingServices\HttpHostingService.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\HostingServices\IHostingService.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\HostingServices\IHostingServiceConfigurationProvider.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetBuildSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetEntry.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetEntryCollection.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetGroup.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetGroupSchema.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetGroupSchemaSet.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetGroupSchemaTemplate.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetGroupTemplate.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetGroupTemplateInterface.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetPostProcessor.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetProfileSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableAssetUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressableScenesManager.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AddressablesFileEnumeration.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\AssetReferenceDrawerUtilities.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\BuiltinSceneCache.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\CacheInitializationSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\CcdFolder.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\GlobalInitialization.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\GroupSchemas\BundledAssetGroupSchema.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\GroupSchemas\ContentUpdateGroupSchema.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\GroupSchemas\PlayerDataGroupSchema.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\KeyDataStore.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\LabelTable.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\OrgData.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\Preferences.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\ProfileDataSourceSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\ProfileGroupType.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\ProfileValueReference.cs
H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8\Editor\Settings\ProjectConfigData.cs
