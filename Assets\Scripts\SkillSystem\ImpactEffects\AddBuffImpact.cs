using NaughtyAttributes;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem.Interactions;

public class AddBuffImpact : IImpactEffect
{
    //查找场景中的ScenceLog脚本
    private ScenceLog scenceLog = GameObject.Find("LogSystem").GetComponent<ScenceLog>();
    public IEnumerator Execute(SkillDeployer deployer)
    {
        //创建一个buff
        //这里要询问日志系统，根据buffTypeID获取buffID
        BuffData buff = Resources.Load<BuffData>("Data/BuffData/" + scenceLog.GetBuffIdByType(deployer.SkillData.buffTypeId[0]));
        if (buff == null)
        {
            Debug.LogError("Can't find buff data with type id " + deployer.SkillData.buffTypeId);
            yield break;
        }
        foreach (var target in deployer.SkillData.attackTargets)
        {
            BuffManager buffManager = target.GetComponent<BuffManager>();
            if (buffManager != null)
            {
                //作用于buff的系数不可为0
                if (deployer.SkillData.coefficient == 0)
                {
                    // Debug.LogError("Coefficient of buff " + deployer.SkillData.skillName + " is 0, can't add buff");
                    yield break;
                }
                //添加buff
                Debug.Log("AddBuffImpact to " + target.name + " with buff " + buff.name + " and coefficient " + deployer.SkillData.coefficient);
                buffManager.AddBuff(buff, 1, deployer.SkillData.coefficient);
            }
        }
        yield break;
    }

}
