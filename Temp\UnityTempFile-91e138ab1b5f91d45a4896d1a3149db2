/target:library
/out:Temp/Unity.RenderPipelines.HighDefinition.Editor.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Unity.RenderPipelines.HighDefinition.Editor.dll.ref
/unsafe
/deterministic
/debug:portable
/optimize+
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll
/reference:Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll
/reference:Library/ScriptAssemblies/Unity.VisualEffectGraph.Editor.dll
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:Assets/AstarPathfindingProject/Plugins/DotNetZip/Pathfinding.Ionic.Zip.Reduced.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/AsyncIO.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/NaCl.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/NetMQ.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Release/netstandard2.0/UnityCodeAssistSynchronizerModel.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Serilog.Sinks.PersistentFile.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Serilog.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Buffers.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Memory.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Runtime.CompilerServices.Unsafe.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Threading.Tasks.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/log4netPlastic.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/unityplastic.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.0.2/Runtime/Newtonsoft.Json.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.render-pipelines.core@10.10.1/Editor/ShaderGenerator/ICSharpCode.NRefactory.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_INPUT_SYSTEM
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:HDRP_1_OR_NEWER
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_42
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:USE_SEARCH_ENGINE_API
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssemblyInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\AssetVersion.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\AutodeskInteractiveMaterialImport.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\CubeLutImporter.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\FBXArnoldSurfaceMaterialDescriptionPreprocessor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\FBXMaterialDescriptionPostprocessor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\HDIESImporterEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\MaterialPostProcessor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\ModelPostProcessor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\NormalMapFilteringTexturePostprocessor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\PhysicalMaterial3DsMaxPreprocessor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\ShaderGraphMaterialsUpdater.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\SketchupMaterialDescriptionPostprocessor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\AssetProcessors\ThreeDSMaterialDescriptionPostprocessor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\BuildProcessors\HDRPPreprocessBuild.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\BuildProcessors\HDRPPreprocessShaders.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Compositor\CompositionFilterUI.Drawer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Compositor\CompositionLayerUI.Drawers.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Compositor\CompositionManagerEditor.Styles.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Compositor\CompositionManagerEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Compositor\CompositionUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Compositor\CompositorWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Compositor\SerializedCompositionFilter.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Compositor\SerializedCompositionLayer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Compositor\SerializedCompositionManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Compositor\SeriallizedShaderProperty.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Compositor\ShaderPropertyUI.Drawers.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Core\Lighting\LightLayerEnumPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Core\TextureCombiner\TextureCombiner.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\HDAnalytics.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\AdditionalShadowDataEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\AmbientOcclusionEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\DiffusionProfileOverrideEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\HDAdditionalLightDataEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\HDIESImporter.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\HDLightEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\HDLightExplorerExtension.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\HDLightUI.ContextualMenu.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\HDLightUI.Handles.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\HDLightUI.Skin.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\HDLightUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\IndirectLightingControllerEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\LightUnit\LightUnitSlider.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\LightUnit\LightUnitSliderSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\ProbeVolume\ProbeVolumeEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\ProbeVolume\ProbeVolumeUI.Drawer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\ProbeVolume\ProbeVolumeUI.Skin.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\ProbeVolume\SerializedProbeVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\ProbeVolumeControllerEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\CameraSettingsUI.Drawers.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDAdditionalReflectionDataEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDBakedReflectionSystem.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDProbeEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDProbeUI.Drawers.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDProbeUI.Handles.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDProbeUI.Skin.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDProbeUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDReflectionProbeEditor.Gizmos.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDReflectionProbeEditor.Preview.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDReflectionProbeEditor.ProbeUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDReflectionProbeEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\HDScreenSpaceReflectionEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\PlanarReflectionProbeEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\ProbeSettingsUI.Drawers.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\ReflectionMenuItem.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\ScreenSpaceRefractionEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\SerializedCameraSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\SerializedHDProbe.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\SerializedHDReflectionProbe.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\SerializedPlanarReflectionProbe.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\SerializedProbeSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\Volume\InfluenceVolumeUI.Drawers.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\Volume\InfluenceVolumeUI.Gizmos.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\Volume\InfluenceVolumeUI.Handles.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\Volume\InfluenceVolumeUI.Skin.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\Volume\InfluenceVolumeUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\Volume\ProxyVolumeUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\Volume\ReflectionProxyVolumeComponentEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\Volume\SerializedInfluenceVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\Volume\SerializedProxyVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Reflection\Volume\SerializedReflectionProxyVolumeComponent.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\SerializedHDLight.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Shadow\ContactShadowsEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Shadow\HDShadowSettingsEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Shadow\MicroShadowingEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\Shadow\ShadowCascadeGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\VolumetricLighting\DensityVolumeEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\VolumetricLighting\DensityVolumeUI.Drawer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\VolumetricLighting\DensityVolumeUI.Skin.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\VolumetricLighting\SerializedDensityVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Lighting\VolumetricLighting\VolumetricMenuItem.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\AxF\AxFGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\BaseShaderPreprocessor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Decal\DecalLayerEnumPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Decal\DecalMenuItems.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Decal\DecalProjectorEditor.Skin.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Decal\DecalProjectorEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Decal\DecalUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Decal\ShaderGraph\CreateDecalShaderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Decal\ShaderGraph\DecalData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Decal\ShaderGraph\DecalGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Decal\ShaderGraph\DecalPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Decal\ShaderGraph\DecalSubTarget.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Decal\ShaderGraph\DecalSubTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\DiffusionProfile\DiffusionProfileMaterialUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\DiffusionProfile\DiffusionProfileSettingsEditor.Styles.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\DiffusionProfile\DiffusionProfileSettingsEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\CreateEyeShaderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\EyeData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\EyeSubTarget.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\EyeSubTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\EyeSurfaceOptionPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\Node\CirclePupilAnimation.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\Node\CorneaRefraction.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\Node\EyeSurfaceTypeDebug.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\Node\IrisLimbalRing.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\Node\IrisOffset.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\Node\IrisOutOfBoundColorClamp.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\Node\IrisUVLocation.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\Node\ScleraIrisBlend.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\Node\ScleraLimbalRing.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Eye\ShaderGraph\Node\ScleraUVLocation.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Fabric\ShaderGraph\CreateFabricShaderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Fabric\ShaderGraph\FabricData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Fabric\ShaderGraph\FabricSubTarget.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Fabric\ShaderGraph\FabricSubTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Fabric\ShaderGraph\FabricSurfaceOptionPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Hair\ShaderGraph\CreateHairShaderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Hair\ShaderGraph\HairAdvancedOptionsPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Hair\ShaderGraph\HairData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Hair\ShaderGraph\HairSubTarget.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Hair\ShaderGraph\HairSubTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\LayeredLit\LayeredLitGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Lit\BaseLitGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Lit\LitGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Lit\LitShaderPreprocessor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Lit\ShaderGraph\CreateHDLitShaderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Lit\ShaderGraph\HDLitData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Lit\ShaderGraph\HDLitSubTarget.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Lit\ShaderGraph\HDLitSubTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Lit\ShaderGraph\LitSurfaceOptionPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Lit\StandardsToHDLitMaterialUpgrader.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\MaterialEditorExtension.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\MaterialExternalReferences.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\MaterialHeaderScope.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Nature\HDSpeedTree8MaterialUpgrader.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\AdvancedOptionsPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\DiffusionProfileMaterialPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\DiffusionProfilePropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\DiffusionProfileShaderProperty.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\DistortionPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\HDBlockFields.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\HDFields.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\HDMetadata.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\HDPropertiesHeader.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\HDShaderPasses.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\HDStructFields.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\HDStructs.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\HDSubShaderUtilities.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\HDSubTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\HDTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Legacy\DecalMasterNode1.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Legacy\EyeMasterNode1.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Legacy\FabricMasterNode1.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Legacy\HDLitMasterNode1.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Legacy\HDUnlitMasterNode1.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Legacy\HairMasterNode1.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Legacy\StackLitMasterNode1.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\LightingSubTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Nodes\DiffusionProfileNode.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Nodes\EmissionNode.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Nodes\ExposureNode.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Nodes\HDSceneColorNode.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Nodes\RayTracingQualityNode.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\PassDescriptorExtension.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\ShaderGraphVersion.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Slots\DefaultMaterialSlot.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Slots\DiffusionProfileInputMaterialSlot.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\SubTargetPropertiesGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\SubTargetPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\SurfaceOptionPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\SurfaceSubTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\TargetData\BuiltinData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\TargetData\HDTargetData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\TargetData\IRequiresData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\TargetData\LightingData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\TargetData\SystemData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\ShaderGraph\Views\DiffusionProfileSlotControlView.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\StackLit\ShaderGraph\CreateStackLitShaderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\StackLit\ShaderGraph\StackLitData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\StackLit\ShaderGraph\StackLitSubTarget.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\StackLit\ShaderGraph\StackLitSubTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\StackLit\ShaderGraph\StackLitSurfaceOptionPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\TerrainLit\StandardsTerrainToHDTerrainLitUpgrader.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\TerrainLit\TerrainLitGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\AdvancedOptionsUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\AxfMainSurfaceInputsUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\AxfSurfaceInputsUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\DecalSortingInputsUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\DecalSurfaceInputsUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\DecalSurfaceOptionsUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\DetailInputsUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\DistortionUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\EmissionUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\HDShaderGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\LayerListUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\LayeringOptionsUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\LayersUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\LightingShaderGraphGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\LitShaderGraphGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\LitSurfaceInputsUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\MaterialUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\MaterialUIBlockList.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\RefractionUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\ShaderGraphUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\SurfaceOptionUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\TessellationOptionsUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\TransparencyUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\UIBlocks\UnlitSurfaceInputsUIBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Unlit\BaseUnlitGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Unlit\ShaderGraph\CreateHDUnlitShaderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Unlit\ShaderGraph\HDUnlitData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Unlit\ShaderGraph\HDUnlitDistortionPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Unlit\ShaderGraph\HDUnlitGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Unlit\ShaderGraph\HDUnlitSubTarget.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Unlit\ShaderGraph\HDUnlitSubTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Unlit\ShaderGraph\HDUnlitSurfaceOptionPropertyBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Unlit\UnlitGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Material\Unlit\UnlitsToHDUnlitUpgrader.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PackageInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\BloomEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\ChannelMixerEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\ChromaticAberrationEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\ColorCurvesEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\DepthOfFieldEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\ExposureEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\FilmGrainEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\LiftGammaGainEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\MotionBlurEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\SerializedGlobalPostProcessSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\ShadowsMidtonesHighlightsEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\TonemappingEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\TrackballUIDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\PostProcessing\VignetteEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RayTracing\ReflectionKernelGenerator.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RayTracing\SolidAngleKernelGenerator.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\BaseUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Camera\HDAdditionalCameraEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Camera\HDCameraEditor.Handlers.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Camera\HDCameraEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Camera\HDCameraUI.Drawers.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Camera\HDCameraUI.Skin.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Camera\SerializedHDCamera.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\CustomPass\CustomPassDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\CustomPass\CustomPassDrawerAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\CustomPass\CustomPassVolumeEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\CustomPass\DrawRenderersCustomPassDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\CustomPass\FullScreenCustomPassDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\HDAssetFactory.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\HDBaseEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\HDEditorCLI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\HDEditorUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\HDRenderPipelineEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\HDRenderPipelineMenuItems.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\HDRenderPipelineUI.Skin.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\HDRenderPipelineUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\HDShaderUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\IUpdateable.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\PathTracing\PathTracingEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Raytracing\GlobalIlluminationEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Raytracing\RayTracingSettingsEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Raytracing\RayTracingShaderPreprocessor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Raytracing\RecursiveRenderingEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Raytracing\SubSurfaceScatteringEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\ScalableSettingLevelParameterEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\DefaultSettingsPanel.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\DiffusionProfileSettingsListUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\EditorDefaultSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\FrameSettingsUI.Drawers.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\FrameSettingsUI.Skin.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\OverridableFrameSettingsArea.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\QualitySettingsPanel.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedDynamicResolutionSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedFrameSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedGlobalDecalSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedGlobalLightLoopSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedGlobalProbeVolumeSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedHDRenderPipelineAsset.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedHDShadowInitParameters.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedLightingQualitySettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedLowResTransparencySettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedPostProcessingQualitySettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedRenderPipelineSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedScalableSetting.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedScalableSettingValue.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedVirtualTexturingSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\Settings\SerializedXRSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\VirtualTexturingSettingsUI.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\RenderPipeline\VolumeComponentWithQualityEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\SceneTemplates\HDRPBasicDxrScenePipeline.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\SceneTemplates\HDRPBasicScenePipeline.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Sky\AtmosphericScattering\FogEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Sky\GradientSky\GradientSkyEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Sky\HDLightingWindowEnvironmentSection.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Sky\HDRISky\HDRISkyEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Sky\PhysicallyBasedSky\PhysicallyBasedSkyEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Sky\SkySettingsEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Sky\StaticLightingSkyEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Sky\VisualEnvironmentEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Upgraders\UpgradeStandardShaderMaterials.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\Outputs\VFXAbstractDistortionOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\Outputs\VFXAbstractParticleHDRPLitOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\Outputs\VFXDistortionMeshOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\Outputs\VFXDistortionPlanarPrimitiveOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\Outputs\VFXDistortionQuadStripOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\Outputs\VFXLitCubeOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\Outputs\VFXLitMeshOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\Outputs\VFXLitPlanarPrimitiveOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\Outputs\VFXLitQuadStripOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\Outputs\VFXLitSphereOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\VFXHDRPBinder.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\VFXGraph\VFXHDRPSubOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Wizard\HDProjectSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Wizard\HDWizard.Configuration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Wizard\HDWizard.UIElement.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Editor\Wizard\HDWizard.Window.cs
