using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem.HID;

public abstract class SkillDeployer : MonoBehaviour
{
    private SkillData skillData;
    public Transform owner; // 技能拥有者
    public SkillData SkillData // 技能管理器提供
    {
        get { return skillData; }
        set { skillData = value;  InitDeplopyer();}
    }

    // 范围选择算法
    protected IAttackSelector selector;
    // 效果算法对象
    protected IImpactEffect impact;
    // 系数选择算法
    protected ICoefficientGenerator coefficientGenerator;
    // 释放检查器
    protected ISkillReleaseChecker releaseChecker;

    // 技能释放完成的回调事件
    public delegate void SkillDeployCompleteHandler(int skillId);
    public event SkillDeployCompleteHandler OnSkillDeployComplete;

    // 初始化释放器
    protected void InitDeplopyer()
    {
        releaseChecker = DeployerConfigFactory.CreateSkillReleaseChecker(skillData);
        selector = DeployerConfigFactory.CreateAttackSelector(skillData);
        impact = DeployerConfigFactory.CreateImpactEffect(skillData, owner);
        coefficientGenerator = DeployerConfigFactory.CreateCoefficientGenerator(skillData);
    }

    // 是否满足施放技能前置条件
    public bool CheckReleaseCondition()
    {
        return releaseChecker.CheckReleaseCondition(skillData);
    }

    // 范围选择
    public void CalculateTargets()
    {
        skillData.attackTargets = selector.SelectTarget(skillData, owner.transform);
    }

    // 系数
    public void GetSkillCoefficient()
    {
        if (coefficientGenerator == null)
        {
            Debug.LogError("coefficientGenerator is null" + skillData.coefficientType);
        }
        if (owner == null)
        {
            Debug.LogError("owner is null");
        }
        if (skillData == null)
        {
            Debug.LogError("skillData is null");
        }
        skillData.coefficient = coefficientGenerator.GenerateCoefficient(skillData, owner.transform);
    }


    // 协程执行效果
    private IEnumerator ExecuteImpact()
    {
        int skillID = skillData.skillId;

        if (impact == null)
        {
            Debug.LogError("impact is null");
            yield break;
        }
        // 假设impact.Execute是异步执行的
        IEnumerator impactCoroutine = impact.Execute(this);
        if (impactCoroutine != null)
        {
            yield return StartCoroutine(impactCoroutine);
        }

        //执行完成后，移除owner上的impact组件
        Destroy(impact as MonoBehaviour); // 这里假设impact是MonoBehaviour的子类
        impact = null; // 清空引用，避免内存泄漏

        // 协程执行完成后触发回调
        NotifySkillDeployComplete(skillID);
        yield break;
    }

    // 效果
    protected void ImpactTargets()
    {
        
        StartCoroutine(ExecuteImpact());
    }

    public bool CanReleaseSkill()
    {
        return releaseChecker.CheckReleaseCondition(skillData);
    }

    public abstract void DeploySkill(); // 供技能管理器调用，由子类实现，定义具体释放策略

    // 触发技能释放完成的回调
    protected void NotifySkillDeployComplete(int skillId)
    {
        OnSkillDeployComplete?.Invoke(skillId); // 触发回调
    }
}