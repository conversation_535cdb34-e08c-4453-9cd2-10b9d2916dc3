/target:library
/out:Temp/Unity.RenderPipelines.HighDefinition.Runtime.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Unity.RenderPipelines.HighDefinition.Runtime.dll.ref
/unsafe
/deterministic
/debug:portable
/optimize+
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:Assets/AstarPathfindingProject/Plugins/DotNetZip/Pathfinding.Ionic.Zip.Reduced.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/AsyncIO.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/NaCl.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/NetMQ.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Release/netstandard2.0/UnityCodeAssistSynchronizerModel.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Serilog.Sinks.PersistentFile.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Serilog.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Buffers.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Memory.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Runtime.CompilerServices.Unsafe.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Threading.Tasks.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/log4netPlastic.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/unityplastic.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.0.2/Runtime/Newtonsoft.Json.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.render-pipelines.core@10.10.1/Editor/ShaderGenerator/ICSharpCode.NRefactory.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_INPUT_SYSTEM
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_VR_MODULE
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:ENABLE_XR_MODULE
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:HDRP_1_OR_NEWER
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_42
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:USE_SEARCH_ENGINE_API
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\AssemblyInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\ComponentUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Compositor\AdditionalCompositorData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Compositor\AlphaInjection.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Compositor\ChromaKeying.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Compositor\CompositionFilter.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Compositor\CompositionLayer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Compositor\CompositionManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Compositor\CompositionProfile.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Compositor\CompositorCameraRegistry.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Compositor\CustomClear.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Compositor\ShaderProperty.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Core\CoreResources\GPUCopy.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Core\Debugging\FrameSettingsFieldAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Core\Migration\IVersionable.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Core\Migration\MigrationDescription.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Core\Migration\MigrationStep.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Core\Textures\EncodeBC6H.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Core\Textures\TextureCache.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Core\Textures\TextureCache2D.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Core\Textures\TextureCacheCubemap.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Core\Utilities\GeometryUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\ColorPickerDebug.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\DebugDisplay.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\DebugLightVolumes.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\DebugOverlay.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\DecalsDebug.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\FalseColorDebug.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\LightingDebug.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\MaterialDebug.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\MipMapDebug.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\RayCountManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\TransparencyDebug.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Debug\VolumeDebug.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Documentation.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\AtmosphericScattering\AtmosphericScattering.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\AtmosphericScattering\ExponentialFog.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\AtmosphericScattering\Fog.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\AtmosphericScattering\VolumetricFog.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\DiffusionProfileOverride.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\GlobalIllumination.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\GlobalIlluminationUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\IndirectLightingController.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Light\HDAdditionalLightData.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Light\HDAdditionalLightData.Types.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Light\HDAdditionalLightData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\LightCookieManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\LightDefinition.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\LightLoop\GlobalLightLoopSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\LightLoop\LightLoop.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\LightUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ProbeVolume\GlobalProbeVolumeSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ProbeVolume\ProbeVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ProbeVolume\ProbeVolumeAsset.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ProbeVolume\ProbeVolumeController.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ProbeVolume\ProbeVolumeLighting.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ProbeVolume\ProbeVolumeManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\HDAdditionalReflectionData.Legacy.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\HDAdditionalReflectionData.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\HDAdditionalReflectionData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\HDProbe.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\HDProbe.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\HDProbeCullState.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\HDProbeCullingResults.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\HDProbeSystem.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\HDRuntimeReflectionSystem.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\PlanarReflectionProbe.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\PlanarReflectionProbe.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\PlanarReflectionProbeCache.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\ReflectionProbeCache.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\ReflectionSystemParameters.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\Volume\InfluenceVolume.Editor.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\Volume\InfluenceVolume.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\Volume\InfluenceVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\Volume\ProxyVolume.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\Volume\ProxyVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\Volume\ReflectionProxyVolumeComponent.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Reflection\Volume\ShapeType.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ScreenSpaceLighting\AmbientOcclusion.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ScreenSpaceLighting\AmbientOcclusion.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ScreenSpaceLighting\SSGIDenoiser.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ScreenSpaceLighting\ScreenSpaceGlobalIllumination.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ScreenSpaceLighting\ScreenSpaceGlobalIllumination.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ScreenSpaceLighting\ScreenSpaceReflection.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ScreenSpaceLighting\ScreenSpaceRefraction.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ScreenSpaceLighting\ShaderVariablesAmbientOcclusion.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\ScreenSpaceLighting\ShaderVariablesScreenSpaceReflection.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\AdditionalShadowData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\ContactShadows.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\HDCachedShadowAtlas.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\HDCachedShadowManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\HDDynamicShadowAtlas.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\HDShadowAtlas.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\HDShadowManager.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\HDShadowManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\HDShadowSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\HDShadowUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\MicroShadowing.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\ScreenSpaceShadowManager.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\ScreenSpaceShadowManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\ScreenSpaceShadowManagerArea.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\ScreenSpaceShadowManagerDirectional.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\ScreenSpaceShadowManagerDirectional.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\ScreenSpaceShadowManagerPunctual.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\Shadow\ScreenSpaceShadowManagerPunctual.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\SphericalHarmonics.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\VolumetricLighting\DensityVolume.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\VolumetricLighting\DensityVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\VolumetricLighting\DensityVolumeManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\VolumetricLighting\Texture3DAtlas.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\VolumetricLighting\VolumetricLighting.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Lighting\VolumetricLighting\VolumetricLightingController.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\AxF\AxF.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\AxF\AxFLTCAreaLight\LtcData.GGX2.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Builtin\BuiltinData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Decal\DBufferManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Decal\Decal.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Decal\DecalProjector.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Decal\DecalProjector.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Decal\DecalSystem.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Decal\GlobalDecalSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\DecalMeshBiasTypeEnum.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\DiffusionProfile\DiffusionProfileSettings.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\DiffusionProfile\DiffusionProfileSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Eye\Eye.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Fabric\Fabric.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Fabric\IBLFilterCharlie.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\GBufferManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\GGXConvolution\GGXConvolution.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\GGXConvolution\IBLFilterGGX.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Hair\Hair.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\IBLFilterBSDF.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\LTCAreaLight\LTCAreaLight.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\LTCAreaLight\LtcData.DisneyDiffuse.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\LTCAreaLight\LtcData.GGX.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Lit\Lit.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\MaterialBlendModeEnum.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\MaterialExtension.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\PreIntegratedFGD\PreIntegratedFGD.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\RenderPipelineMaterial.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\SharedRTManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\SphericalCapPivot\PivotData.GGX.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\SphericalCapPivot\SPTDistribution.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\StackLit\StackLit.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\SubsurfaceScattering\SubSurfaceScattering.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\SubsurfaceScattering\SubsurfaceScatteringManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\SubsurfaceScattering\SubsurfaceScatteringManagerRT.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\Unlit\Unlit.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Material\VTBufferManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PackageInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\Bloom.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\ChannelMixer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\ChromaticAberration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\ColorAdjustments.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\ColorCurves.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\DepthOfField.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\Exposure.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\FilmGrain.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\LensDistortion.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\LiftGammaGain.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\MotionBlur.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\PaniniProjection.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\ShadowsMidtonesHighlights.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\SplitToning.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\Tonemapping.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\Vignette.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Components\WhiteBalance.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\CustomPostProcessing\CustomPostProcessInjectionPoint.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\CustomPostProcessing\CustomPostProcessVolumeComponent.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\GlobalPostProcessSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\IPostProcessComponent.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\PostProcessSystem.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\PostProcessSystem.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\PostProcessing\Shaders\UberPostFeatures.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Accumulation\SubFrameManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Camera\HDAdditionalCameraData.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Camera\HDAdditionalCameraData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Camera\HDCamera.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Camera\HDCameraFrameHistoryType.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\CullingGroupManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\GlobalLightingQualitySettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\GlobalLowResolutionTransparencySettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\GlobalPostProcessingQualitySettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDGPUAsyncTask.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDProfileId.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipeline.Debug.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipeline.LightLoop.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipeline.LookDev.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipeline.PostProcess.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipeline.Prepass.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipeline.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipeline.RenderGraphUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipeline.SubsurfaceScattering.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipeline.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipelineAsset.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipelineAsset.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipelineEditorResources.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipelineEditorResources.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderPipelineRayTracingResources.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDRenderQueue.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDStencilUsage.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\HDStringConstants.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\MRTBufferManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\PathTracing\PathTracing.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDDiffuseDenoiser.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDDiffuseShadowDenoiser.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDRaytracingAmbientOcclusion.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDRaytracingAmbientOcclusion.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDRaytracingDeferredLightLoop.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDRaytracingDeferredLightLoop.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDRaytracingIndirectDiffuse.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDRaytracingIndirectDiffuse.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDRaytracingLightCluster.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDRaytracingManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDRaytracingRecursiveRenderer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDRaytracingReflection.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDRaytracingReflection.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDReflectionDenoiser.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDSimpleDenoiser.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDTemporalFilter.RenderGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\HDTemporalFilter.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\LightCluster.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\RayTracingMode.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\RayTracingSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\RecursiveRendering.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\Shaders\ShaderVariablesRaytracing.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Raytracing\Shaders\ShaderVariablesRaytracingLightLoop.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\AOV\AOVBuffers.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\AOV\AOVRequest.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\AOV\AOVRequestBuilder.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\AOV\AOVRequestData.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\AOV\AOVRequestDataCollection.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\AOV\RenderOutputProperties.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\CustomPass\CustomPass.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\CustomPass\CustomPassContext.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\CustomPass\CustomPassInjectionPoint.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\CustomPass\CustomPassUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\CustomPass\CustomPassVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\DrawRenderersCustomPass.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\FullScreenCustomPass.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPass\MipGenerator.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPipelineResources.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\RenderPipelineResources.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\SceneViewDrawMode.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Settings\CaptureSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Settings\FrameSettings.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Settings\FrameSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Settings\FrameSettingsHistory.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Settings\RenderPipelineSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Settings\ScalableSetting.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Settings\ScalableSettingSchema.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Settings\ScalableSettingSchemaId.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Settings\ScalableSettingValue.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\ShaderPass\ShaderPass.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Utility\BlueNoise.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Utility\HDUtils.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Utility\PowerOfTwoTextureAtlas.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Utility\Texture2DAtlas.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Utility\Texture2DAtlasDynamic.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\Utility\Texture3DAtlasDynamic.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\VirtualTexturingSettingsSRP.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\XR\GlobalXRSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\XR\XRLayout.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\XR\XRPass.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\RenderPipeline\XR\XRSystem.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Scripting\GameObjectExtension.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\ShaderLibrary\ShaderVariablesGlobal.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\ShaderLibrary\ShaderVariablesXR.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\GradientSky\GradientSky.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\GradientSky\GradientSkyRenderer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\HDRISky\HDRISky.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\HDRISky\HDRISkyRenderer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\PhysicallyBasedSky\PhysicallyBasedSky.Migration.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\PhysicallyBasedSky\PhysicallyBasedSky.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\PhysicallyBasedSky\PhysicallyBasedSkyRenderer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\PhysicallyBasedSky\ShaderVariablesPhysicallyBasedSky.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\SkyManager.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\SkyRenderer.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\SkyRenderingContext.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\SkySettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\SkyUpdateContext.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\StaticLightingSky.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Sky\VisualEnvironment.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\CameraCache.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\CameraPositionSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\CameraSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\CameraSettingsUtilities.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\DiffusionProfileHashTable.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\HDAdditionalSceneViewSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\HDBakingUtilities.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\HDRenderPipelinePreferences.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\HDRenderUtilities.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\HDTextureUtilities.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\ProbeCapturePositionSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\ProbeSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\ProbeSettingsUtilities.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\SceneObjectIDMap.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\TypeInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\Utilities\VolumeComponentWithQuality.cs
H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1\Runtime\VFXGraph\Utility\PropertyBinders\HDRPCameraBinder.cs
