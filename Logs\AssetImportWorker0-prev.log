Using pre-set license
Built from '2020.3/staging' branch; Version is '2020.3.42f1 (7ade1201f527) revision 8052242'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 32607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
H:\Works\2020.3.42f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
H:/Works/TS
-logFile
Logs/AssetImportWorker0.log
-srvPort
62165
Successfully changed project path to: H:/Works/TS
H:/Works/TS
Using Asset Import Pipeline V2.
Refreshing native plugins compatible for Editor in 108.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.42f1 (7ade1201f527)
[Subsystems] Discovering subsystems at path H:/Works/2020.3.42f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/Works/TS/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 SUPER (ID=0x2783)
    Vendor:   
    VRAM:     11999 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'H:/Works/2020.3.42f1/Editor/Data/Managed'
Mono path[1] = 'H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56024
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001052 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 107.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.557 seconds
Domain Reload Profiling:
	ReloadAssembly (557ms)
		BeginReloadAssembly (50ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (455ms)
			LoadAssemblies (50ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (139ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (20ms)
			SetupLoadedEditorAssemblies (216ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (4ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (107ms)
				BeforeProcessingInitializeOnLoad (13ms)
				ProcessInitializeOnLoadAttributes (67ms)
				ProcessInitializeOnLoadMethodAttributes (25ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.007158 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 105.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.400 seconds
Domain Reload Profiling:
	ReloadAssembly (1401ms)
		BeginReloadAssembly (111ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (17ms)
		EndReloadAssembly (1250ms)
			LoadAssemblies (108ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (309ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (72ms)
			SetupLoadedEditorAssemblies (699ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (105ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (475ms)
				ProcessInitializeOnLoadMethodAttributes (16ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (1ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6468 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 288.5 MB.
System memory in use after: 289.0 MB.

Unloading 45 unused Assets to reduce memory usage. Loaded Objects now: 6926.
Total: 3.770900 ms (FindLiveObjects: 0.349700 ms CreateObjectMapping: 0.183700 ms MarkObjects: 3.202600 ms  DeleteObjects: 0.033700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Resources/Data/Player/Alice.asset
  artifactKey: Guid(40ce26f1d82f30d4c9c7abde19141087) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/Player/Alice.asset using Guid(40ce26f1d82f30d4c9c7abde19141087) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e96add649486421f814438b5abd2598a') in 0.032707 seconds 
========================================================================
Received Import Request.
  Time since last request: 39.931222 seconds.
  path: Assets/Resources/Data/BuffData/87.asset
  artifactKey: Guid(65169495d68879046823bf3cefa55bf0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/87.asset using Guid(65169495d68879046823bf3cefa55bf0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b3a7cf49e8a01fe1b45e42d47d4d50b0') in 0.004810 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014891 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 4.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.399 seconds
Domain Reload Profiling:
	ReloadAssembly (2399ms)
		BeginReloadAssembly (190ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (2146ms)
			LoadAssemblies (213ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (582ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (112ms)
			SetupLoadedEditorAssemblies (1049ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (4ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (5ms)
				BeforeProcessingInitializeOnLoad (177ms)
				ProcessInitializeOnLoadAttributes (809ms)
				ProcessInitializeOnLoadMethodAttributes (49ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (17ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 270.1 MB.
System memory in use after: 270.6 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6930.
Total: 3.876200 ms (FindLiveObjects: 0.469100 ms CreateObjectMapping: 0.187200 ms MarkObjects: 3.183700 ms  DeleteObjects: 0.034300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012541 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.238 seconds
Domain Reload Profiling:
	ReloadAssembly (1238ms)
		BeginReloadAssembly (112ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (39ms)
		EndReloadAssembly (1090ms)
			LoadAssemblies (106ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (304ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (60ms)
			SetupLoadedEditorAssemblies (521ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (4ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (411ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 270.3 MB.
System memory in use after: 270.7 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6933.
Total: 3.740800 ms (FindLiveObjects: 0.381800 ms CreateObjectMapping: 0.196200 ms MarkObjects: 3.132900 ms  DeleteObjects: 0.029100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 257.840071 seconds.
  path: Assets/Resources/Data/BuffData/89.asset
  artifactKey: Guid(c15ad5ad03511464d924de6f7bbe17b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/89.asset using Guid(c15ad5ad03511464d924de6f7bbe17b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fc91eeb6dfe96a22036468df6cc4a71b') in 0.022334 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.330555 seconds.
  path: Assets/Resources/Data/BuffData/79.asset
  artifactKey: Guid(19b99c08aeab3e045b1085f24da5bfbd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/79.asset using Guid(19b99c08aeab3e045b1085f24da5bfbd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cecfecbe7d5de9034b89a0cf40931471') in 0.004305 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.421739 seconds.
  path: Assets/Resources/Data/BuffData/77.asset
  artifactKey: Guid(4e4c3917836110244865e9355359eefc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/77.asset using Guid(4e4c3917836110244865e9355359eefc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e882b9f28792ebe72b9a6e6087f02d0e') in 0.002575 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.007130 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.332 seconds
Domain Reload Profiling:
	ReloadAssembly (1332ms)
		BeginReloadAssembly (160ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (61ms)
		EndReloadAssembly (1133ms)
			LoadAssemblies (149ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (350ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (57ms)
			SetupLoadedEditorAssemblies (523ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (413ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.47 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 270.3 MB.
System memory in use after: 270.8 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6936.
Total: 3.542200 ms (FindLiveObjects: 0.317200 ms CreateObjectMapping: 0.165400 ms MarkObjects: 3.034200 ms  DeleteObjects: 0.024200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 28.629674 seconds.
  path: Assets/Scripts/attributeProperty/AttributeProperty.cs
  artifactKey: Guid(5986721925d3a8b48b40968ac9e66c92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/attributeProperty/AttributeProperty.cs using Guid(5986721925d3a8b48b40968ac9e66c92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '13641ca3bd0385dfd17d008084286a6c') in 0.019474 seconds 
========================================================================
Received Import Request.
  Time since last request: 25.355843 seconds.
  path: Assets/Resources/Prefabs/Player/Player.prefab
  artifactKey: Guid(38280c34d6df4a04eb2cebd012d21fce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Player/Player.prefab using Guid(38280c34d6df4a04eb2cebd012d21fce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '995212c084875d28813143e3603259fc') in 0.288831 seconds 
========================================================================
Received Import Request.
  Time since last request: 12.543285 seconds.
  path: Assets/Scripts/attributeProperty/Player/PlayerController.cs
  artifactKey: Guid(0b6f76aa093c5e34985919ad4ab33dff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/attributeProperty/Player/PlayerController.cs using Guid(0b6f76aa093c5e34985919ad4ab33dff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'aaf32012bdf9b5c3bcccae09b86a1e67') in 0.004798 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.541634 seconds.
  path: Assets/Resources/skillIndicator/StatusIndicators/Scripts/Components/PointPlayerAngleMissile.cs
  artifactKey: Guid(39f0757d079c4e94a8079ef6516d5b65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/skillIndicator/StatusIndicators/Scripts/Components/PointPlayerAngleMissile.cs using Guid(39f0757d079c4e94a8079ef6516d5b65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bea0aa1af0606b5ca0f798123febaa7c') in 0.003145 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.019339 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.232 seconds
Domain Reload Profiling:
	ReloadAssembly (1232ms)
		BeginReloadAssembly (116ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (42ms)
		EndReloadAssembly (1078ms)
			LoadAssemblies (120ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (304ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (52ms)
			SetupLoadedEditorAssemblies (523ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (417ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.7 MB.
System memory in use after: 274.1 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6946.
Total: 4.110100 ms (FindLiveObjects: 0.592900 ms CreateObjectMapping: 0.208700 ms MarkObjects: 3.277400 ms  DeleteObjects: 0.030200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018205 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.643 seconds
Domain Reload Profiling:
	ReloadAssembly (2644ms)
		BeginReloadAssembly (250ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (79ms)
		EndReloadAssembly (2320ms)
			LoadAssemblies (277ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (860ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (107ms)
			SetupLoadedEditorAssemblies (927ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (4ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (243ms)
				ProcessInitializeOnLoadAttributes (662ms)
				ProcessInitializeOnLoadMethodAttributes (13ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.7 MB.
System memory in use after: 274.2 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6949.
Total: 4.206400 ms (FindLiveObjects: 0.318200 ms CreateObjectMapping: 0.166300 ms MarkObjects: 3.689400 ms  DeleteObjects: 0.031300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016840 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.239 seconds
Domain Reload Profiling:
	ReloadAssembly (1240ms)
		BeginReloadAssembly (110ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (41ms)
		EndReloadAssembly (1094ms)
			LoadAssemblies (106ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (320ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (53ms)
			SetupLoadedEditorAssemblies (526ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (416ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.7 MB.
System memory in use after: 274.2 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6952.
Total: 3.717900 ms (FindLiveObjects: 0.319000 ms CreateObjectMapping: 0.176800 ms MarkObjects: 3.190800 ms  DeleteObjects: 0.030300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014975 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.242 seconds
Domain Reload Profiling:
	ReloadAssembly (1242ms)
		BeginReloadAssembly (108ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (1099ms)
			LoadAssemblies (108ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (311ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (57ms)
			SetupLoadedEditorAssemblies (544ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (432ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.7 MB.
System memory in use after: 274.2 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6955.
Total: 3.933700 ms (FindLiveObjects: 0.328900 ms CreateObjectMapping: 0.172200 ms MarkObjects: 3.403300 ms  DeleteObjects: 0.028000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017209 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 2.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.788 seconds
Domain Reload Profiling:
	ReloadAssembly (2789ms)
		BeginReloadAssembly (205ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (55ms)
		EndReloadAssembly (2515ms)
			LoadAssemblies (227ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (540ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (145ms)
			SetupLoadedEditorAssemblies (1207ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (13ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (301ms)
				ProcessInitializeOnLoadAttributes (863ms)
				ProcessInitializeOnLoadMethodAttributes (21ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (20ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.7 MB.
System memory in use after: 274.2 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6958.
Total: 6.467300 ms (FindLiveObjects: 0.935000 ms CreateObjectMapping: 0.269900 ms MarkObjects: 5.219900 ms  DeleteObjects: 0.040900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
