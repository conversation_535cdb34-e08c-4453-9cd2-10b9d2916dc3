using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
using UnityEngine.InputSystem;
using Utils;


public class AttributeProperty : MonoBehaviour
{
    // Start is called before the first frame update
    // ========= Base =================
    protected CharacterType characterType;
    // ========= EXP =================
    public float pickupRange; //经验拾取范围

    // ========= MOVE =================
    public float moveSpeed; //攻速
    public float evasionRate; //闪避率
    // ========= ATTACK =================
    public float attackSpeed; //攻速
    public float attackRange; //攻击距离
    public float attackDamage; //攻击伤害
    public float bulletSpeed; //子弹速度
    // ======== HEALTH ==========
    public delegate void ConditionChangeHandler(CharacterType characterType, int value);
    public event ConditionChangeHandler HpChanged;
    public int maxHealth; //最大生命值
    public float timeInvincible; //无敌时间
    public float invincibleTimer; //无敌计时器
    public bool isInvincible; //是否处于无敌
    protected int _overHeal = 0;
    protected int _lastHealthLoss = 0; //上次受到的伤害
    public int LastHealthLoss
    {
        get { return _lastHealthLoss; }
    }
    public int overHeal
    {
        get { return _overHeal; }
    }
    protected int _health;
    public int health
    {
        get { return _health; }
    }
    public float HealthPercentage
    {
        get { return (float)(_health / _validMaxHealth); }
    }
    // ======== SKILL ==========
    public int maxSkillNum; //最大技术数目;
    public float initActiveSkillCD = 10f; //主动技能初始cd
    public float passiveSkillHaste = 0f; //被动技能cd
    public float activeSkillHaste = 0f; //主动技能cd

    //二级属性
    public float extraPickupRange = 0f; //额外经验拾取范围
    public float extraEvasionRate = 0f; //额外闪避率
    public float extraMoveSpeed = 0f; //额外攻击距离
    public float extraAttackRange = 0f; //额外攻击距离
    public float extraAttackSpeed = 0f; //额外攻速
    public float extraAttackDamage = 0f; //额外攻击伤害
    public float extraBulletSpeed = 0f; //额外子弹速度
    public int healthChangePerSecond = 0; //每秒生命变化
    public int extraHealth = 0; //额外生命值
    public int extraSkillNum = 0; //额外技能栏
    public float expCoefficient = 1f; //经验系数
    //百分比二级属性
    public float attackSpeedPct = 1f; //百分比攻速加成
    public float attackRangePct = 1f; //百分比攻击距离加成
    public float attackDamagePct = 1f; //百分比攻速加成
    public float healthChangePerSecondPct = 1f; //每秒百分比生命变化
    public float takeDamagePct = 1f; //百分比增伤
    public float moveSpeedPct = 1f; //百分比移速
    public float controlStatusPct = 1f; //百分比受控制时间 （韧性）
    //生存时间
    private float _survivalTime = 0;
    public float survivalTime
    {
        get { return _survivalTime; }
    }
    //控制状态
    public Dictionary<string, int> _controlStatus = new Dictionary<string, int>() { };  //控制状态
    public Dictionary<string, Dictionary<bool, System.Action>> _statusFuncMap; //记录状态开关对应的控制函数
    public Dictionary<string, int> _statusPctLevel = new Dictionary<string, int>()
    {
        {"DisableAttack", 0},     //无法攻击
        {"DisableMove", 0}, //无法移动
        {"DisableDamage", 0}, //无法造成伤害(依然有攻击动画)
    }; //记录状态的优先级‘

    //是否处于控制状态，任意伤害
    public bool IsUnderControl
    {
        get
        {
            //_controlStatus 中至少有一个状态的层数大于0
            int _sum = 0;
            foreach (var status in _controlStatus)
            {
                _sum += status.Value;
            }
            return _sum > 0;
        }
    }
    // 升级激活技能
    protected CharacterSkillSystem _skillSystem;
    //光环技能
    protected Dictionary<int, bool> sustainableAuraSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> SustainableAuraSkills
    {
        get { return sustainableAuraSkills; }
        set { sustainableAuraSkills = value; }
    }
    //死亡技能
    protected Dictionary<int, bool> deathSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> DeathSkills
    {
        get { return deathSkills; }
        set { deathSkills = value; }
    }
    //攻击技能
    protected Dictionary<int, bool> attackSkills = new Dictionary<int, bool>() { };

    public Dictionary<int, bool> AttackSkills
    {
        get { return attackSkills; }
        set { attackSkills = value; }
    }
    //移动触发技能
    protected Dictionary<int, bool> moveSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> MoveSkills
    {
        get { return moveSkills; }
        set { moveSkills = value; }
    }
    //召唤触发技能
    protected Dictionary<int, bool> summonSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> SummonSkills
    {
        get { return summonSkills; }
        set { summonSkills = value; }
    }
    //被攻击触发技能
    protected Dictionary<int, bool> healthChangeSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> HealthChangeSkills
    {
        get { return healthChangeSkills; }
        set { healthChangeSkills = value; }
    }

    //增加/删除指定buff时触发技能
    protected Dictionary<int, bool> addOrRemoveBuffSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> AddOrRemoveBuffSkills
    {
        get { return addOrRemoveBuffSkills; }
        set { addOrRemoveBuffSkills = value; }
    }

    //控制状态触发技能
    protected Dictionary<int, bool> controlStatusSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> ControlStatusSkills
    {
        get { return controlStatusSkills; }
        set { controlStatusSkills = value; }
    }

    //进入控制状态触发技能
    private Dictionary<int, bool> enterControlStatusSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> EnterControlStatusSkills
    {
        get { return enterControlStatusSkills; }
        set { enterControlStatusSkills = value; }
    }

    //脱离控制状态触发技能
    private Dictionary<int, bool> leaveControlStatusSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> LeaveControlStatusSkills
    {
        get { return leaveControlStatusSkills; }
        set { leaveControlStatusSkills = value; }
    }

    //击杀触发技能
    protected Dictionary<int, bool> killSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> KillSkills
    {
        get { return killSkills; }
        set { killSkills = value; }
    }

    //元素反应触发技能
    protected Dictionary<int, bool> elementReactionSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> ElementReactionSkills
    {
        get { return elementReactionSkills; }
        set { elementReactionSkills = value; }
    }

    //升级激活技能
    protected Dictionary<int, bool> levelUpSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> LevelUpSkills
    {
        get { return levelUpSkills; }
        set { levelUpSkills = value; }
    }

    //过度治疗触发技能
    protected Dictionary<int, bool> overHealSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> OverHealSkills
    {
        get { return overHealSkills; }
        set { overHealSkills = value; }
    }

    //获取buff触发技能
    protected Dictionary<int, bool> addBuffSkills = new Dictionary<int, bool>() { };
    public Dictionary<int, bool> AddBuffSkills
    {
        get { return addBuffSkills; }
        set { addBuffSkills = value; }
    }

    //技能对应的触发器

    //========= TRIGGERS =================
    //整合了不同的触发环境，集合了所有针对该触发环境响应的升级选项
    public virtual void SustainableAuraTrigger()
    {
        //遍历所有的SustainableAura
        foreach (var aura in sustainableAuraSkills)
        {
            if (aura.Value)
            {
                _skillSystem.AttackUseSkill(aura.Key);
            }
        }
    }

    public virtual void AddBuffTrigger()
    {
        //获取Buff时触发
        //遍历所有的AddBuffSkills
        foreach (var aura in AddBuffSkills)
        {
            if (aura.Value)
            {
                _skillSystem.AttackUseSkill(aura.Key);
            }
        }
    }


    public virtual void KillTrigger()
    {

    }

    public virtual void SummonTrigger()
    {
        //召唤判定

    }

    public virtual void LevelUpTrigger()
    {
        //升级判定
        //遍历所有的LevelUpSkills
        foreach (var aura in LevelUpSkills)
        {
            if (aura.Value)
            {
                _skillSystem.AttackUseSkill(aura.Key);
            }
        }
    }

    public virtual void MoveTrigger()
    {
        //移动判定
        //遍历所有的MoveSkills
        foreach (var aura in MoveSkills)
        {
            if (aura.Value)
            {
                _skillSystem.AttackUseSkill(aura.Key);
            }
        }

    }

    public virtual void HurtTrigger()
    {
        //遍历所有的SustainableAura
        foreach (var aura in HealthChangeSkills)
        {
            if (aura.Value)
            {
                _skillSystem.AttackUseSkill(aura.Key);
            }
        }
    }
    public virtual void DeathTrigger()
    {
        //遍历所有的SustainableAura
        foreach (var aura in DeathSkills)
        {
            if (aura.Value)
            {
                _skillSystem.AttackUseSkill(aura.Key);
            }
        }
    }

    public virtual void ControlStatusTrigger()
    {
        //遍历所有的SustainableAura
        foreach (var aura in ControlStatusSkills)
        {
            if (aura.Value)
            {
                _skillSystem.AttackUseSkill(aura.Key);
            }
        }
    }

    public virtual void EnterControlTrigger()
    {
        //遍历所有的SustainableAura
        foreach (var aura in EnterControlStatusSkills)
        {
            if (aura.Value)
            {
                _skillSystem.AttackUseSkill(aura.Key);
            }
        }
    }

    public virtual void LeaveControlTrigger()
    {
        //遍历所有的SustainableAura
        foreach (var aura in LeaveControlStatusSkills)
        {
            if (aura.Value)
            {
                _skillSystem.AttackUseSkill(aura.Key);
            }
        }
    }


    public virtual void OverHealTrigger()
    {

        foreach (var aura in OverHealSkills)
        {
            if (aura.Value)
            {
                _skillSystem.AttackUseSkill(aura.Key);
            }
        }
    }

    public virtual float _validPickupRange
    {
        get { return pickupRange + extraPickupRange; }
    }
    public virtual float _validMaxCoolDown
    {
        get { return initActiveSkillCD * MathUtils.skillHasteToCooldownReduction(activeSkillHaste); }
    }

    public virtual float _validPassiveSkillCDPct
    {
        get { return MathUtils.skillHasteToCooldownReduction(passiveSkillHaste); }
    }


    public virtual float _validMoveSpeed //有效移速
    {
        get { return (moveSpeed + extraMoveSpeed) * moveSpeedPct; }
    }
    public virtual float _validAttackSpeed //有效攻速
    {
        get { return (attackSpeed + extraAttackSpeed) * attackSpeedPct; }
    }
    // 有效闪避率，至多60%
    public virtual float _validEvasionRate
    {
        get { return Mathf.Clamp(evasionRate + extraEvasionRate, 0, 0.6f); }
    }
    // 有效攻击范围
    public virtual float _validAttackRange
    {
        get { return (attackRange + extraAttackRange) * attackRangePct; }
    }
    // 有效攻击伤害
    public virtual float _validAttackDamage
    {
        get { return (attackDamage + extraAttackDamage) * attackDamagePct; }
    }
    // 有效子弹速度
    public virtual float _validBulletSpeed
    {
        get { return bulletSpeed + extraBulletSpeed; }
    }
    // 有效最大生命值
    public virtual int _validMaxHealth
    {
        get { return maxHealth + extraHealth; }
    }

    public virtual int _validMaxSkillNum
    {
        get { return maxSkillNum + extraSkillNum; }
    }


    // 获取属性值的方法
    public T GetProperty<T>(string propertyName)
    {
        // Use reflection to get property value
        var property = GetType().GetProperty(propertyName);
        return (T)property.GetValue(this, null);
    }
    // 使用反射的方法修改属性
    public void SetProperty<T>(string propertyName, T value)
    {
        // 使用反射设置属性值
        GetType().GetProperty(propertyName)?.SetValue(this, value, null);
    }

    //修改属性的方法
    public virtual void ChangeHealth(int amount)
    {
        //如果处于无敌状态，不受伤害
        if (isInvincible & amount < 0)
        {
            return;
        }
        if (amount < 0)
        {
            _lastHealthLoss = amount;
        }
        //如果生命值发生了改变
        if (amount != 0)
        {
            //触发生命值改变事件
            HpChanged?.Invoke(characterType, amount);
        }
    }

    //控制状态接口
    public virtual void UpdateControlStatus(string status, int layerNum)
    {
        //从_controlStatus中获取status对应的层数，如果不存在该键值则获取0

        int _previousLayerNum;
        _controlStatus.TryGetValue(status, out _previousLayerNum);
        _controlStatus[status] = Mathf.Clamp(_previousLayerNum + layerNum, 0, Mathf.Abs(_previousLayerNum + layerNum));
        //如果状态发生了改变
        if (_previousLayerNum != _controlStatus[status])
        {
            //如果不存在这个状态的对应函数，不执行，记录到debug日志
            if (!_statusFuncMap.ContainsKey(status))
            {
                Debug.Log("没有找到" + status + "对应的方法");
                return;
            }
            //若之前的状态为关闭，则执行开启函数
            else if (_previousLayerNum == 0)
            {
                _statusFuncMap[status][true]();
            }
            //若之前状态为开启，则执行关闭函数
            else if (_controlStatus[status] == 0)
            {
                _statusFuncMap[status][false]();
            }
            else
            {
                //如果状态发生了改变，但是仍然处于开启状态，不执行
                return;
            }
        }
    }

    //次级属性接口

    public void SetAdditionalProperty(string key, string value)
    {
        //获取名称为key的属性
        var property = GetType().GetProperty(key);
        //如果属性存在
        if (property != null)
        {
            //获取属性的类型
            var type = property.PropertyType;
            //如果属性的类型是float
            if (type == typeof(float))
            {
                //将value转换为float类型
                float _value;
                float.TryParse(value, out _value);
                //设置属性值
                property.SetValue(this, _value, null);
            }
            //如果属性的类型是int
            else if (type == typeof(int))
            {
                //将value转换为int类型
                int _value;
                int.TryParse(value, out _value);
                //设置属性值
                property.SetValue(this, _value, null);
            }
            //如果属性的类型是bool
            else if (type == typeof(bool))
            {
                //将value转换为bool类型
                bool _value;
                bool.TryParse(value, out _value);
                //设置属性值
                property.SetValue(this, _value, null);
            }
            //如果属性的类型是string
            else if (type == typeof(string))
            {
                //设置属性值
                property.SetValue(this, value, null);
            }
            else
            {
                Debug.Log("没有找到" + key + "对应的解析方法");
            }
        }
    }
    public void SetAdditionalSkills(string var_name, string key, string value)
    {

        var parentType = typeof(AttributeProperty);
        var property = parentType.GetProperty(var_name);
        //如果属性存在
        if (property != null)
        {
            //获取属性的类型
            var type = property.PropertyType;
            //如果属性的类型是int
            if (type == typeof(Dictionary<int, bool>))
            {
                //将value转换为float类型
                bool _value;
                bool.TryParse(value, out _value);
                int _key;
                int.TryParse(key, out _key);
                //在property中添加key-value对
                if (property.GetValue(this, null) == null)
                {
                    property.SetValue(this, new Dictionary<int, bool>() { { _key, _value } }, null);
                }
                else
                {
                    Dictionary<int, bool> dict = property.GetValue(this, null) as Dictionary<int, bool>;
                    dict[_key] = _value;
                }
            }
            else
            {
                Debug.Log("没有找到" + key + "对应的解析方法");
            }
        }
    }
    //订阅事件
    protected virtual void Start()
    {
        // 订阅伤害管理器的事件
        ScenceLog.Instance.Subscribe(this);
    }

    //取消订阅事件
    protected virtual void OnDestroy()
    {
        // 当敌人被销毁时，取消订阅
        ScenceLog.Instance.Unsubscribe(this);
    }

    //更新生存时间
    protected virtual void Update()
    {
        _survivalTime += Time.deltaTime;
    }
    //获取属性值
    public float GetPropertyValue(string propertyName)
    {
        //使用反射获取属性值
        var property = GetType().GetProperty(propertyName);
        if (property == null)
        {
            Debug.Log("没有找到" + propertyName + "对应的属性");
            return 0;
        }
        if (property.PropertyType == typeof(int))
        {
            return (int)property.GetValue(this, null) * 1f; //将int转换为float类型，以便后续计算
        }
        else if (property.PropertyType == typeof(float))
        {
            return (float)property.GetValue(this, null);
        }
        else
        {
            Debug.Log("没有找到" + propertyName + "对应的解析方法");
            return 0;
        }
    }
    
    public void ResetHealth()
    {
        _health = Mathf.Clamp(_health, 0, _validMaxHealth);
    }

}