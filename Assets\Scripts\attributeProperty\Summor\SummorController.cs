using Pathfinding;
using System.Collections;
using System.Collections.Generic;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.InputSystem;
using TMPro;

public class SummorController : AttributeProperty
{
    //======== CHARDATA ========
    private CharacterStats _characterStats;
    private float _healthChangeTime = 0.0f;
    protected float _attackGap;

    // =========== MOVEMENT ==============
    protected Rigidbody2D _rigidbody2d;
    protected AIPath _aIPath;
    protected AIDestinationSetter _aIDestinationSetter;

    // ======== SKILL ==========
    public string[] skills;

    // ======== ATTACK ==========
    protected GameObject _target = null;
    protected bool _isTargetLocked = false;
    protected bool _isTargetUpdate = false;
    protected GameObject _player;
    public float attackSearchRange = 0.2f;
    public GameObject target
    { 
        get { return _target;} 
    }
    //======== DISPLAY ==========
    public TMP_Text debugInfo;


    protected virtual void Awake()
    {
        // ======= CHARDATA ========
        _characterStats = GetComponent<CharacterStats>();

        // ======= INIT ===========
        InitCharStats();
        _health = _validMaxHealth;
        _rigidbody2d = GetComponent<Rigidbody2D>();
        debugInfo = GetComponentInChildren<TMP_Text>();

    }

    // Start is called before the first frame update
    protected override void Start()
    {
        //获取玩家的操纵角色,存在于Player层中
        _player = GameObject.FindWithTag("Player");
        _aIPath = GetComponent<AIPath>();
        _aIDestinationSetter = GetComponent<AIDestinationSetter>();
        _skillSystem = GetComponent<CharacterSkillSystem>();
    }

    // Update is called once per frame
    protected override void Update()
    {
        // ================= HEALTH ====================
        if (isInvincible)
        {
            invincibleTimer -= Time.deltaTime;
            if (invincibleTimer < 0)
                isInvincible = false;
        }
        UpdateHealth(); //结算每秒生命值变化
        //  ========Movement==========
        UpdateValidTarget();
        UpdateAI();
        // ===Attack=====
        _attackGap = Mathf.Clamp(_attackGap, 0, 1 / _validAttackSpeed); //修正攻击间隔，这里是为了防止有的buff造成攻速过慢，buff消失后长时间无法攻击
        Attack();
        // ======== SKILL ==========
        SustainableAuraTrigger();
        // ======== DISPLAY ==========
        debugInfo.text = _health.ToString();
    }

    protected virtual void UpdateHealth()
    {
        if ((_healthChangeTime <= 0.0f) & (healthChangePerSecond != 0))
        {
            ChangeHealth(healthChangePerSecond);
            _healthChangeTime = timeInvincible;
        }
        else
        {
            _healthChangeTime -= Time.deltaTime;
        }
    }

    public override void ChangeHealth(int amount)
    {
        if (amount <= 0)
        {
            if (isInvincible)
                return;
            int _validDamage = Mathf.FloorToInt(amount * takeDamagePct);
            _lastHealthLoss = _validDamage;
            _health = Mathf.Clamp(_health + _validDamage, 0, _validMaxHealth);
            isInvincible = true;
            invincibleTimer = timeInvincible;
            HurtTrigger();
        }
        else
        {

            _overHeal = _health + amount - _validMaxHealth;
            _health = Mathf.Clamp(_health + amount, 0, _validMaxHealth);
            if (_overHeal > 0)
            {
                OverHealTrigger();
            }
        }

        if (_health <= 0)
        {
            Destroy(gameObject);
        }
    }


    protected virtual void UpdateValidTarget() 
    {
        if (_target == null & _isTargetLocked)
        {
            _isTargetUpdate = true;
            _isTargetLocked = false;
        }

        //如果不存在目标，则锁定目标
        if (_target == null)
        {
            //锁定的目标为距离玩家单位最近的目标
            //搜索_player附近的圆形范围内的敌人
            Collider2D[] _enemys = Physics2D.OverlapCircleAll(_player.transform.position, attackSearchRange, LayerMask.GetMask("Enemy"));
            if (_enemys.Length > 0)
            {
                //在敌人中随机选择，离玩家越近的敌人被选中的概率越大
                float[] _distance = new float[_enemys.Length];
                float _sum = 0;
                for (int i = 0; i < _enemys.Length; i++)
                {
                    _distance[i] = 1.0f / Vector2.Distance(_enemys[i].transform.position, _player.transform.position);
                    _sum += _distance[i];
                }
                float _rand = UnityEngine.Random.Range(0, _sum);
                for (int i = 0; i < _enemys.Length; i++)
                {
                    _rand -= _distance[i];
                    if (_rand <= 0)
                    {
                        _target = _enemys[i].gameObject;
                        _isTargetUpdate = true;
                        _isTargetLocked = true;
                        break;
                    }
                }
            }
        }
        //存在攻击目标时，在几种特定情况下可以转换目标
        else
        {
            //召唤物距离玩家过远(2倍攻击距离)
            if (Vector2.Distance(_player.transform.position, transform.position) > 2 * attackSearchRange)
            {
                _target = null;
            }
            //目标死亡(_target自动置为null)
            //目标处于无法被选定的状态(待增加检测)
            //特殊的召回指令
        }
    }

    protected virtual void UpdateAI()
    {
        if (_isTargetUpdate)
        {
            if (_target == null)
            {
                //如果没有目标，则移动到玩家附近
                _aIDestinationSetter.target = _player.transform;
            }
            else
            {
                _aIDestinationSetter.target = _target.transform;
            }
            _isTargetUpdate = false;
        }
    }

    protected virtual void Attack()
    { 
        //如果攻击间隔大于0，则减少攻击间隔
        if (_attackGap > 0)
        {
            _attackGap -= Time.deltaTime;
            return;
        }
        //如果存在锁定目标且为敌人，则执行攻击判定
        if (_target != null)
        {
            //判定攻击范围内所有敌人
            Collider2D[] _enemys = Physics2D.OverlapCircleAll(transform.position, _validAttackRange, LayerMask.GetMask("Enemy"));
            //如果存在敌人，则对敌人造成伤害
            if (_enemys.Length > 0)
            {
                foreach (Collider2D _enemy in _enemys)
                {
                    _enemy.GetComponent<AttributeProperty>().ChangeHealth(-(int)_validAttackDamage);
                }
                _attackGap = 1 / _validAttackSpeed;
            }
        }
    }


    private void InitCharStats()
    {
        // 一级属性
        // ======== HEALTH ==========
        invincibleTimer = -1.0f;
        maxHealth = _characterStats.maxHealth;
        timeInvincible = _characterStats.timeInvincible;
        if (_characterStats.skills != null)
        {
            skills = new string[_characterStats.skills.Length];
            for (int i = 0; i < _characterStats.skills.Length; i++)
            {
                skills[i] = _characterStats.skills[i];
            }
        }
        else { skills = null; }

        //========= ATTACK =================
        attackSpeed = _characterStats.attackSpeed;
        attackDamage = _characterStats.attackDamage;
        attackRange = _characterStats.attackRange;
        bulletSpeed = _characterStats.bulletSpeed;
        _attackGap = 0.0f;
        // =========== MOVEMENT ==============
        moveSpeed = _characterStats.moveSpeed;
        // ========== BUILD ================
        maxSkillNum = _characterStats.maxSkillNum;
        initActiveSkillCD = _characterStats.initActiveSkillCD;
        passiveSkillHaste = _characterStats.passiveSkillHaste;
        activeSkillHaste = _characterStats.activeSkillHaste;
        //二级属性
        // ======== HEALTH ==========
        attackSpeedPct = _characterStats.attackSpeedPct; //百分比攻速加成
        attackDamagePct = _characterStats.attackDamagePct; //百分比攻速加成
        healthChangePerSecond = _characterStats.healthChangePerSecond; //每秒生命变化
        healthChangePerSecondPct = _characterStats.healthChangePerSecond; //每秒百分比生命变化
        takeDamagePct = _characterStats.takeDamagePct; //百分比增伤
        moveSpeedPct = _characterStats.moveSpeedPct; //百分比移速
    }
}
