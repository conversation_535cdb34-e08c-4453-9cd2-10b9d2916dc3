%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec6a3d0267be4c249a394b8e806ee823, type: 3}
  m_Name: 49
  m_EditorClassIdentifier: 
  skillId: 49
  skillName: Skill - ConvertPlayerAttackSpeedToAllyAttackSpeedLevel1
  description: "\u57FA\u4E8E\u73A9\u5BB6\u653B\u51FB\u901F\u5EA6\uFF0C\u5C0F\u5E45\u5EA6\u63D0\u5347\u6240\u6709\u53CB\u519B\u653B\u51FB\u901F\u5EA6"
  skillCd: 3
  cdRemain: 0
  skillDeployer: 0
  skillReleaseChecker: ConstantProbability
  skillReleaseCheckerJson: "{\u201Dprobability\u201D:\u201D1\u201D}"
  attenuationType: 0
  coefficientType: 1
  attackCoeffient: 0.5
  CoeffientBaseTags: []
  scriptName: 1
  propertyField: _validAttackSpeed
  directionType: 0
  coefficient: 0
  buffTypeId: de070000
  attachedProperty: []
  attackDistance: 1
  attackAngle: 360
  attackTargetTags:
  - Summor
  - Tower
  attackTargets: []
  impactType: 2
  durationTime: 0
  attackInterval: 0
  owner: {fileID: 0}
  prefabName: 
  skillPrefab: {fileID: 0}
  animationName: 
  hitFxName: 
  hitFxPrefab: {fileID: 0}
  level: 0
  attackType: 1
  scatterType: 0
  selectorType: 0
  skillIndicator: 
  skillIconName: 
  skillIcon: {fileID: 0}
  disappearType: 0
