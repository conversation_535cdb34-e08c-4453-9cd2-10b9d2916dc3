/target:library
/out:Temp/Unity.VisualEffectGraph.Editor.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Unity.VisualEffectGraph.Editor.dll.ref
/deterministic
/debug:portable
/optimize+
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll
/reference:Library/ScriptAssemblies/Unity.Timeline.dll
/reference:Library/ScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:Assets/AstarPathfindingProject/Plugins/DotNetZip/Pathfinding.Ionic.Zip.Reduced.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/AsyncIO.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/NaCl.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/NetMQ.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Release/netstandard2.0/UnityCodeAssistSynchronizerModel.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Serilog.Sinks.PersistentFile.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/Serilog.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Buffers.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Memory.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Runtime.CompilerServices.Unsafe.dll
/reference:Assets/Plugins/CodeAssist/Editor/ExternalReferences/System.Threading.Tasks.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/log4netPlastic.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.collab-proxy@2.1.0/Lib/Editor/PlasticSCM/unityplastic.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.0.2/Runtime/Newtonsoft.Json.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.render-pipelines.core@10.10.1/Editor/ShaderGenerator/ICSharpCode.NRefactory.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_INPUT_SYSTEM
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_42
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:USE_SEARCH_ENGINE_API
/define:VFX_HAS_HDRP
/define:VFX_HAS_TIMELINE
/define:VFX_HAS_UNIVERSAL
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Compiler\VFXCodeGenerator.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Compiler\VFXExpressionGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Compiler\VFXExpressionMapper.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Compiler\VFXGraphCompiledData.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Compiler\VFXShaderWriter.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Compiler\VFXUniformMapper.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\Element3D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXBitField.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXColorField.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXControl.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXEnumField.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXEnumValuePopup.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXFlipBookField.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXLabeledField.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXMatrix4x4Field.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXReorderableList.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXSliderField.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXStringField.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXStringFieldProvider.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXStringFieldPushButton.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXVector2Field.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXVector3Field.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Controls\VFXVector4Field.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Core\VFXConverter.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Core\VFXEnums.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Core\VFXLibrary.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Core\VFXSerializer.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Core\VFXSettingAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Data\VFXData.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Data\VFXDataMesh.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Data\VFXDataOutputEvent.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Data\VFXDataParticle.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Data\VFXDataSpawner.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Debug\DotGraphOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Debug\VFXUIDebug.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXAttributeExpression.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXBuiltInExpression.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionAbstract.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionAbstractBoolOperation.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionAbstractFloatOperation.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionAbstractNumericOperation.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionAbstractUintOperation.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionAbstractValues.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionBakeCurve.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionBakeGradient.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionCamera.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionCast.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionColor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionCombine.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionContext.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionExtractComponent.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionFlow.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionLoadTexture.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionMath.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionMesh.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionNoise.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionRandom.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionSampleAttributeMap.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionSampleCurve.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionSampleGradient.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionSampleSDF.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionSampleTexture2D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionSampleTexture2DArray.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionSampleTexture3D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionSampleTextureCube.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionSampleTextureCubeArray.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionSpawnerState.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionStrips.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionTextureDim.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionTextureValues.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Expressions\VFXExpressionTransform.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\FilterPopup\VFXBlockProvider.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\FilterPopup\VFXFilterWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Gizmo\VFXGizmo.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Gizmo\VFXGizmoUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Blackboard\VFXBlackboard.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Blackboard\VFXBlackboardCategory.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Blackboard\VFXBlackboardField.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Blackboard\VFXBlackboardPropertyView.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Blackboard\VFXBlackboardRow.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Controllers\Controller.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Controllers\IControlledElement.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\3D\Preview3D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\3D\Rotate3DManipulator.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXBlockController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXContextController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXContextDataAnchorController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXDataAnchorController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXDataEdgeController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXFlowAnchorController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXFlowEdgeController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXGroupNodeController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXNodeController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXOperatorAnchorController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXOperatorController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXParameterController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXParameterNodeController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXSettingController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\Controllers\VFXSlotContainerController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXBlockUI.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXContextUI.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXDataAnchor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXDataEdge.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXEdgeConnector.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXEdgeDragInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXEditableDataAnchor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXElement.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXFlowAnchor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXFlowEdge.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXGroupNode.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXMultiOperatorEdit.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXNodeUI.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXOperatorUI.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXOutputDataAnchor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXParameterUI.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Elements\VFXStickyNote.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\VFXComponentBoard.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\VFXTypeDefinition.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\VFXViewPreference.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\VFXViewWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Controller\VFXGraphUndoCursor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Controller\VFXGraphValidation.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Controller\VFXSystemController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Controller\VFXViewController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Controller\VFXViewControllerExpressions.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Controller\VFXViewControllerUndo.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\BoolPropertyRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\ColorPropertyRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\CurvePropertyRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\GradientPropertyRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\ListPropertiesRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\NumericPropertiesRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\ObjectPropertyRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\PropertyRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\SimplePropertiesRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\SpaceablePropertiesRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\StringPropertyRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\VFXParameterEnumValuePropertyRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\Vector3PropertyRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\Properties\VectorPropertiesRM.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\VFXConvertSubgraph.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\VFXCopy.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\VFXCopyPasteCommon.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\VFXNodeProvider.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\VFXPaste.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\GraphView\Views\VFXView.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Inspector\AdvancedVisualEffectEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Inspector\GizmoController.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Inspector\VFXAssetEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Inspector\VFXBlockEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Inspector\VFXContextEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Inspector\VFXManagerEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Inspector\VFXParameterEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Inspector\VFXSlotContainerEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Inspector\VisualEffectEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Manipulators\DownClickable.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Manipulators\SuperCollapser.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Attribute\AttributeFromCurve.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Attribute\AttributeFromMap.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Attribute\AttributeMassFromVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Attribute\SetCustomAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\CameraHelper.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Collision\CollisionAABox.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Collision\CollisionBase.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Collision\CollisionCylinder.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Collision\CollisionDepth.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Collision\CollisionPlane.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Collision\CollisionSDF.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Collision\CollisionSphere.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Color\ColorOverLife.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\FlipBook\FlipbookPlay.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Forces\ConformToSDF.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Forces\ConformToSphere.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Forces\Drag.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Forces\Force.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Forces\ForceHelper.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Forces\Gravity.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Forces\Turbulence.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Forces\VectorFieldForce.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\GPUEvent\GPUEventAlways.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\GPUEvent\GPUEventOnDie.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\GPUEvent\GPUEventRate.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Kill\KillAABox.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Kill\KillSphere.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Orientation\ConnectTarget.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Orientation\Orient.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Output\CameraFade.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Output\SubpixelAA.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\PositionAABox.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\PositionBase.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\PositionCircle.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\PositionCone.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\PositionDepth.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\PositionLine.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\PositionMesh.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\PositionSDF.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\PositionSequential.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\PositionSphere.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\PositionTorus.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Position\TileWarp.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\SetAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Size\ScreenSpaceSize.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Spawn\VFXAbstractSpawner.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Spawn\VFXSpawnerBurst.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Spawn\VFXSpawnerBurstOld.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Spawn\VFXSpawnerConstantRate.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Spawn\VFXSpawnerCustomWrapper.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Spawn\VFXSpawnerPeriodicBurst.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Spawn\VFXSpawnerSetAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Spawn\VFXSpawnerVariableRate.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Update\Age.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Update\AngularEulerIntegration.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Update\BackupOldPosition.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Update\EulerIntegration.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Update\Reap.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\VFXBlockUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Velocity\VelocityBase.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Velocity\VelocityDirection.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Velocity\VelocityRandomize.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Velocity\VelocitySpeed.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Velocity\VelocitySpherical.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\Implementations\Velocity\VelocityTangent.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\VFXBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Blocks\VFXSubgraphBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\IVFXSubRenderer.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXAbstractParticleOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXAbstractRenderedOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXBasicCubeOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXBasicEvent.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXBasicGPUEvent.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXBasicInitialize.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXBasicSpawner.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXBasicUpdate.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXCubeTestOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXDecalOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXLineOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXLineStripOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXMeshOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXOutputEvent.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXPlanarPrimitiveHelper.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXPlanarPrimitiveOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXPointOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXQuadStripOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\Implementations\VFXStaticMeshOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\VFXBlockSubgraphContext.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\VFXCameraSort.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\VFXContext.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\VFXMultiMeshHelper.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\VFXOutputUpdate.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\VFXSRPSubOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Contexts\VFXSubgraphContext.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\AABoxVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Absolute.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Acos.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Add.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\AgeOverLifetime.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\AppendVector.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Asin.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Atan.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Atan2.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\BitwiseAnd.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\BitwiseComplement.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\BitwiseLeftShift.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\BitwiseOr.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\BitwiseRightShift.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\BitwiseXor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Branch.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Ceiling.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\ChangeSpace.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\CircleArea.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Clamp.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\ColorLuma.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Condition.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\ConeVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\ConstructMatrix.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Cosine.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\CrossProduct.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\CrossProductDeprecated.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\CurlNoise.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\CylinderVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Deprecated\CellularNoise.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Deprecated\NoiseBaseOld.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Deprecated\PerlinNoise.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Deprecated\SanitizeHelper.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Deprecated\SimplexNoise.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Deprecated\ValueNoise.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Discretize.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Distance.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\DistanceToLine.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\DistanceToPlane.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\DistanceToSphere.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Divide.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\DotProduct.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Epsilon.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Exp.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Floor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Fractional.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\GetCustomAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\GetSpawnCount.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\HSVtoRGB.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\InverseLerp.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\InverseTRSMatrix.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Length.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Lerp.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\LoadTexture2D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\LoadTexture2DArray.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\LoadTexture3D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Log.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\LogicalAnd.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\LogicalNand.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\LogicalNor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\LogicalNot.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\LogicalOr.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\LookAt.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\MainCamera.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Maximum.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\MeshVertexCount.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Minimum.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Modulo.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Multiply.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Negate.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Noise.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\NoiseBase.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Normalize.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\OneMinus.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\OrientedBoxVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\PerParticleTotalTime.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\PeriodicTotalTime.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Pi.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\PolarToRectangular.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\PositionDepth.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Power.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\ProbabilitySampling.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\RGBtoHSV.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Random.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Reciprocal.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\RectangularToPolar.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\RectangularToSpherical.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Remap.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\RemapToNegOnePosOne.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\RemapToZeroOne.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Rotate2D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Rotate3D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Round.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SampleAttributeMap.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SampleBezier.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SampleCurve.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SampleGradient.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SampleMesh.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SamplePointCache.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SampleSDF.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SampleTexture2D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SampleTexture2DArray.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SampleTexture3D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SampleTextureCube.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SampleTextureCubeArray.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Saturate.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SawtoothWave.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Sequential3D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SequentialCircle.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SequentialLine.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Sign.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Sine.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SineWave.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Smoothstep.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SpawnState.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SphereVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SphericalToRectangular.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SquareRoot.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SquareWave.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SquaredDistance.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\SquaredLength.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Step.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Subtract.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Switch.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Swizzle.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\Tangent.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\TextureDimensions.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\TorusVolume.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\TransformDirection.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\TransformMatrix.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\TransformPosition.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\TransformVector.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\TransformVector4.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\TransposeMatrix.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\TriangleWave.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\ViewportToWorldPoint.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\VoroNoise2D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\Implementations\WorldToViewportPoint.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\VFXAbstractOperatorNew.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\VFXOperator.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\VFXOperatorUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Operators\VFXSubgraphOperator.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Parameters\Deprecated\VFXBuiltInParameter.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Parameters\VFXAttributeParameter.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Parameters\VFXCurrentAttributeParameter.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Parameters\VFXDynamicBuiltInParameter.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Parameters\VFXInlineOperator.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Parameters\VFXParameter.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Parameters\VFXSourceAttributeParameter.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotAnimationCurve.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotBool.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotColor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotDirection.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotEncapsulated.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotFlipBook.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotFloat.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotFloat2.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotFloat3.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotFloat4.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotGradient.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotInt.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotMatrix4x4.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotMesh.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotObject.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotOrientedBox.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotPosition.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotTexture2D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotTexture2DArray.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotTexture3D.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotTextureCube.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotTextureCubeArray.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotTransform.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotUint.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\Implementations\VFXSlotVector.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\Slots\VFXSlot.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\VFXErrorManager.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\VFXGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\VFXModel.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\VFXParameterInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\VFXSlotContainerModel.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\VFXSystemNames.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Models\VFXUI.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\PackageInfo.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\ShaderGraph\VFXShaderGraphParticleOutput.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\ShaderGraph\VFXShaderGraphPostProcessor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXBoxGizmos.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXCircleGizmos.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXConeGizmos.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXCylinderGizmo.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXLineGizmos.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXPlaneGizmos.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXProperty.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXPropertyAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXSphereGizmos.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXTorusGizmos.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXTransformGizmos.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXTypeExtension.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXTypeUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXTypes.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Types\VFXTypesGizmos.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\DotGraph\DotAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\DotGraph\DotElement.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\DotGraph\DotGraph.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\EventTester\VFXEventTesterWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\ExposedProperty\ExposedPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\PropertyBinding\VFXBinderEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\PropertyBinding\VFXPropertyBinderEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\VectorFieldImporter\Editor\VectorFieldImporter.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\VectorFieldImporter\Editor\VectorFieldImporterEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\VisualEffectActivationBehaviourInspector.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\pCache\BakeTool\PointCacheBakeTool.Mesh.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\pCache\BakeTool\PointCacheBakeTool.Texture.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\pCache\BakeTool\PointCacheBakeTool.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\pCache\Importer\PointCacheImporter.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\pCache\Operator\VFXOperatorPointCache.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\pCache\PCache.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\pCache\PointCache.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utilities\pCache\PointCacheAsset.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utils\VFXContextBorder.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utils\VFXDebugWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utils\VFXResources.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\Utils\VFXSystemBorder.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\VFXAssetEditorUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1\Editor\VisualElementExtensions.cs
