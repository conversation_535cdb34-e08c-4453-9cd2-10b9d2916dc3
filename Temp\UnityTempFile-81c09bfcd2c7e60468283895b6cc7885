/target:library
/out:Temp/Unity.Localization.Editor.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Unity.Localization.Editor.dll.ref
/deterministic
/debug:portable
/optimize+
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/Unity.Addressables.Editor.dll
/reference:Library/ScriptAssemblies/Unity.Addressables.dll
/reference:Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.013.dll
/reference:Library/ScriptAssemblies/Unity.Localization.dll
/reference:Library/ScriptAssemblies/Unity.ResourceManager.dll
/reference:Library/ScriptAssemblies/Unity.TextMeshPro.dll
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll
/reference:H:/Works/TS/Library/PackageCache/com.unity.localization@1.4.5/Editor/Unity.Localization.ThirdParty.Editor.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:ADDRESSABLES_V1
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_INPUT_SYSTEM
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_PROPERTY_VARIANTS
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SAVE_ASSET_IF_DIRTY
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:MODULE_AUDIO
/define:NET_4_6
/define:PACKAGE_TMP
/define:PACKAGE_UGUI
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_42
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:USE_SEARCH_ENGINE_API
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\AddressableEntryNotFoundException.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Addressables\AddressableGroupRules.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Addressables\AnalyzeRuleBase.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Addressables\AssetTableAnalyzeRule.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Addressables\GroupResolver.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Addressables\LocaleAnalyzeRule.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Addressables\StringTableAnalyzeRule.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\AssemblyInfo.cs
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Asset Pipeline\LocalizationAssetPostProcessor.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Asset Pipeline\LocalizationBuildPlayer.cs"
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\AssetNotPersistentException.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Attributes\CollectionExtensionAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\EditorAddressablesInterface.cs
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Extension Methods\LazyLoadExtendedExtensionMethods.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Extension Methods\RectExtensionMethods.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Extension Methods\SerializedPropertyExtensionMethods.cs"
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Icons\EditorIcons.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\LocalizationSettingsMenuItems.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Platform\Android\BuildPlayerAndroid.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Platform\Android\GradleProjectSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Platform\Android\Player.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Platform\Utility\FallbackLocaleHelper.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Platform\iOS\BuildPlayerIOS.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Platform\iOS\PBXProjectExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Platform\iOS\Player.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\CSV\CSV.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\CSV\Columns\ColumnMapping.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\CSV\Columns\CsvColumns.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\CSV\Columns\IKeyColumn.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\CSV\Columns\KeyIdColumns.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\CSV\Columns\LocaleColumns.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\CSV\CsvExtension.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\CSV\CsvExtensionPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\CSV\MenuItems.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Core\LocalizeComponent.cs
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Device Simulator\LocalizationDeviceSimulatorPlugin.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Device Simulator\PackageLanguageMenu.cs"
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\Columns\ColumnMapping.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\Columns\KeyColumn.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\Columns\KeyCommentColumn.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\Columns\KeyMetadataColumn.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\Columns\LocaleColumn.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\Columns\LocaleCommentColumn.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\Columns\LocaleMetadataColumn.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\Columns\SheetColumn.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\GoogleSheets.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\GoogleSheetsExtension.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\GoogleSheetsExtensionPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\NewSheetProperties.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\PushColumnSheetRequest.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\SheetsServiceProvider.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\SheetsServiceProvider.deprecated.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Google\SheetsServiceProviderEditor.cs
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\TextMesh Pro\LocalizeComponent_TMP.cs"
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\UGUI\LocalizeComponent_UGUI.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Xliff\CommonInterfaces.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Xliff\ExporterWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Xliff\ListExtensions.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Xliff\MenuItems.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Xliff\TypeVersionCheck.cs
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Xliff\V1.2\xliff 1.2.cs"
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Xliff\V1.2\xliff-core-1.2-strict.cs
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Xliff\V2\xliff 2.0.cs"
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Xliff\V2\xliff_core_2.0.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Xliff\Xliff.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Plugins\Xliff\XliffDocument.cs
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Property Variants\ContextualPropertyMenu.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Property Variants\GameObjectLocalizerEditor.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Property Variants\ScenePropertyTracker.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Property Variants\TrackedEnumPropertyDrawer.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Property Variants\TrackedLocalizedPropertyDrawer.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Property Variants\TrackedObjectFactory.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Property Variants\TrackedObjectPropertyDrawer.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Property Variants\TrackedPropertyDrawer.cs"
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Reporting\ITaskReporter.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Reporting\ProgressBarReporter.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Reporting\ProgressReporter.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Reporting\TaskTimerReporter.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Search\AssetTableSearchProvider.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Search\ColumnSelectors.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Search\FilterIds.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Search\QueryBuilder\Blocks\CollectionGroupFilterBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Search\QueryBuilder\Blocks\CollectionNameFilterBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Search\QueryBuilder\Blocks\MetadataTypeFilterBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Search\QueryBuilder\Blocks\MetadataValueFilterBlock.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Search\StringTableSearchProvider.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Search\TableSearchProvider.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Settings\AssetTableCollection.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Settings\CollectionExtension.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Settings\LocalizationEditorEvents.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Settings\LocalizationEditorSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Settings\LocalizationProjectSettings.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Settings\LocalizationTableCollection.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Settings\LocalizationTableCollectionCache.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\Settings\StringTableCollection.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Addressables\AddressableGroupRulesEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Addressables\GroupResolverPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\CharacterSet\CharacterSetMenuItems.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\CharacterSet\ExportCharacterSetWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Components\LocalizeStringEventEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\DrivenPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\GameViewLanguageMenu.cs
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Localized Reference\LocalizedAssetPropertyDrawer.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Localized Reference\LocalizedAssetTablePropertyDrawer.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Localized Reference\LocalizedReferencePicker.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Localized Reference\LocalizedReferencePropertyDrawer.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Localized Reference\LocalizedStringPropertyDrawer.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Localized Reference\LocalizedStringTablePropertyDrawer.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Localized Reference\LocalizedTablePropertyDrawer.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Localized Reference\TableEntryTreeView.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Localized Reference\TableTreeView.cs"
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Metadata\MetadataCollectionField.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Metadata\PlatformOverridePropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\ProjectLocalePopupField.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Pseudo\CharacterSubstitutorPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Pseudo\ExpanderPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Pseudo\PseudoLocaleEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Resources.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\SceneControlsWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Settings\Locale\CustomLocaleUIWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Settings\Locale\LocaleEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Settings\Locale\LocaleGeneratorListView.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Settings\Locale\LocaleGeneratorWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Settings\Locale\LocaleIdentifierPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Settings\Locale\LocalesProviderPropertyDrawer.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Settings\LocalizationPreferencesProvider.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Settings\LocalizationSettingsEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Settings\LocalizationSettingsProvider.cs
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Smart Format\GlobalVariableGroupEditor.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Smart Format\GlobalVariableGroupList.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Smart Format\PersistentVariablePropertyDrawer.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Smart Format\PersistentVariablesSourcePropertyDrawer.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Smart Format\SmartFormatField.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Smart Format\SmartFormatterPropertyField.cs"
"H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Smart Format\TimeFormatterPropertyDrawer.cs"
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\AssetTableEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\AssetTableListView.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\AssetTableTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\EditAssetTables.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\GenericAssetTableListView.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\GenericAssetTableListViewColumns.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\GenericAssetTableTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\LocalizationTableCollectionEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\LocalizationTableEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\LocalizationTablesWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\ProjectCollectionsTableSelector.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\ProjectTablesPopup.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\SerializedTableEntryReference.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\SerializedTableReference.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\SharedTableDataEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\StringTableEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\StringTableListView.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\StringTableTreeViewItem.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\TableCollectionEditorAttribute.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\TableCreator.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\TableEditor.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Tables\TableEntrySelected.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\TreeViewPopupWindow.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\AssetUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\HelpBoxFactory.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\ManagedReferenceReorderableList.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\ManagedReferenceUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\MetadataReorderableList.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\PathHelper.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\PropertyDrawerExtended.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\ReorderManipulator.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\ReorderableList.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\ReorderableListExtended.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\TypeUtility.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\Utility\UndoScope.cs
H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5\Editor\UI\XLIFF\XliffVersionPopup.cs
