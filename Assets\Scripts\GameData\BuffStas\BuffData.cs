using System;
using System.Collections.Generic;
using UnityEngine;
using NaughtyAttributes;
using System.Diagnostics.Tracing;

[Serializable]
public class StringDictData
{
    public string key;
    public string value;

    public StringDictData(string key, string value)
    {
        this.key = key;
        this.value = value;
    }
}



[CreateAssetMenu(menuName = "BuffData", fileName = "buff")]
public class BuffData : ScriptableObject
{
    //id类别
    [BoxGroup("ID")]
    public int id; //buff unique id
    [BoxGroup("ID")]
    public int buffTypeId; //buff 类型
    [BoxGroup("ID")]
    public string buffName;
    [BoxGroup("ID")]
    public string buffTypeName;
    [BoxGroup("ID")]
    public int buffLevel; //buff 等级

    //二级属性
    [BoxGroup("额外属性")]
    public float extraPickupRange; //额外拾取范围
    public float extraAttackRange; //额外攻击距离
    [BoxGroup("额外属性")]
    public float extraAttackSpeed; //额外攻速
    [BoxGroup("额外属性")]
    public float extraAttackDamage; //额外攻击伤害
    [BoxGroup("额外属性")]
    public float extraBulletSpeed; //额外子弹速度
    [BoxGroup("额外属性")]
    public float healthChangePerSecond; //每秒生命变化
    [BoxGroup("额外属性")]
    public int extraHealth; //额外生命值
    [BoxGroup("额外属性")]
    public int extraSkillNum; //额外技能栏
    [BoxGroup("额外属性")]
    public float extraEvasionRate; //额外闪避率
    [BoxGroup("额外属性")]
    public float passiveSkillHaste; //被动技能冷却加速
    [BoxGroup("额外属性")]
    public float activeSkillHaste;  //主动技能冷却加速

    //百分比二级属性
    [BoxGroup("属性百分比加成")]
    public float attackSpeedPct = 1f; //百分比攻速加成
    [BoxGroup("属性百分比加成")]
    public float attackRangePct = 1f; //百分比攻击距离加成
    [BoxGroup("属性百分比加成")]
    public float attackDamagePct = 1f; //百分比攻速加成
    [BoxGroup("属性百分比加成")]
    public float healthChangePerSecondPct = 1f; //每秒百分比生命变化
    [BoxGroup("属性百分比加成")]
    public float takeDamagePct = 1f; //百分比增伤

    [BoxGroup("属性百分比加成")]
    public float moveSpeedPct = 1f; //百分比移速
    [BoxGroup("属性百分比加成")]
    public float controlStatusPct = 1f; //百分比受控制时间 （韧性）



    //status(deprecated)
    private string[] _statusTypeNames = new string[] {null, "Frozen", "Rage", "Blind", "Immobilized"};
    [BoxGroup("控制状态")]
    [Dropdown("_statusTypeNames")]
    public string statusTypeNames;

    //buff属性
    [BoxGroup("持续时间")]
    public float lifetime = 1f; //初始持续时间
    [BoxGroup("最大可叠加层数")]
    public int maxStackLayer = 1;//最大叠加层数


    //public GameObject particleEffect;
    //public Sprite buffIcon;
    //[TextArea(2, 3)]
    //public string description;


    [Flags]
    public enum activeLayer
    {
        Player = 1, //00001
        Tower = 2,  //00010
        Enemy = 4,  //00100
        Summor = 8  //01000
    }
    [BoxGroup("对象")]
    [SerializeField]
    public activeLayer activeLayers;


    [BoxGroup("数据"), Label("次级属性修改")]
    [ReorderableList]
    public List<StringDictData> additionalProperty;

    [BoxGroup("数据"), Label("技能修改")]
    [ReorderableList]
    public List<String> additionalSkills;


    public bool isActiveLayer(activeLayer current)
    {
        return (activeLayers & current) != 0;
    }

    public bool isLayerActive(string layerName)
    {
        activeLayer layer;
        if (Enum.TryParse(layerName, out layer))
        {
            return isActiveLayer(layer);
        }
        else
        {
            return false;
        }
    }

    public LayerMask Layers()
    {
        //返回所有勾选的layer
        LayerMask layerMask = 0;
        if (isLayerActive("Player"))
        {
            layerMask |= LayerMask.GetMask("Player");
        }
        if (isLayerActive("Tower"))
        {
            layerMask |= LayerMask.GetMask("Tower");
        }
        if (isLayerActive("Enemy"))
        {
            layerMask |= LayerMask.GetMask("Enemy");
        }
        if (isLayerActive("Summor"))
        {
            layerMask |= LayerMask.GetMask("Summor");
        }
        return layerMask;
    }

}