Using pre-set license
Built from '2020.3/staging' branch; Version is '2020.3.42f1 (7ade1201f527) revision 8052242'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 32607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
H:\Works\2020.3.42f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
H:/Works/TS
-logFile
Logs/AssetImportWorker0.log
-srvPort
58621
Successfully changed project path to: H:/Works/TS
H:/Works/TS
Using Asset Import Pipeline V2.
Refreshing native plugins compatible for Editor in 128.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.42f1 (7ade1201f527)
[Subsystems] Discovering subsystems at path H:/Works/2020.3.42f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/Works/TS/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 SUPER (ID=0x2783)
    Vendor:   
    VRAM:     11999 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'H:/Works/2020.3.42f1/Editor/Data/Managed'
Mono path[1] = 'H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56948
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001006 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 100.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.522 seconds
Domain Reload Profiling:
	ReloadAssembly (522ms)
		BeginReloadAssembly (62ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (413ms)
			LoadAssemblies (61ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (126ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (18ms)
			SetupLoadedEditorAssemblies (194ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (101ms)
				BeforeProcessingInitializeOnLoad (10ms)
				ProcessInitializeOnLoadAttributes (58ms)
				ProcessInitializeOnLoadMethodAttributes (22ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.007969 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 108.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.510 seconds
Domain Reload Profiling:
	ReloadAssembly (1511ms)
		BeginReloadAssembly (111ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (15ms)
		EndReloadAssembly (1360ms)
			LoadAssemblies (126ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (376ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (71ms)
			SetupLoadedEditorAssemblies (724ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (109ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (498ms)
				ProcessInitializeOnLoadMethodAttributes (16ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (1ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.06 seconds
Refreshing native plugins compatible for Editor in 1.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6468 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 288.5 MB.
System memory in use after: 289.0 MB.

Unloading 45 unused Assets to reduce memory usage. Loaded Objects now: 6926.
Total: 4.185600 ms (FindLiveObjects: 0.320900 ms CreateObjectMapping: 0.166700 ms MarkObjects: 3.655300 ms  DeleteObjects: 0.041600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Resources/Data/SkillData/79.asset
  artifactKey: Guid(a6673cd161a8ef34e8daa80da76fe7ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/79.asset using Guid(a6673cd161a8ef34e8daa80da76fe7ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '68f5e10e0d19d2c2ef79db7d0478c225') in 0.036022 seconds 
